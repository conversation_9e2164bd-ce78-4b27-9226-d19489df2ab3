{"name": "vibe-kraft", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3100", "build": "next build", "start": "next start -p 3100", "lint": "next lint"}, "dependencies": {"@ai-sdk/cohere": "^1.2.10", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@ai-sdk/togetherai": "^0.2.14", "@ai-sdk/ui-utils": "^1.2.11", "@auth/core": "latest", "@codesandbox/nodebox": "^0.1.9", "@hookform/resolvers": "^3.9.1", "@monaco-editor/react": "latest", "@next-auth/prisma-adapter": "latest", "@novnc/novnc": "^1.6.0", "@prisma/client": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@react-pdf/renderer": "^4.1.7", "@tanstack/react-query": "^5.75.5", "@types/crypto-js": "^4.2.2", "@types/dockerode": "^3.3.39", "@types/express": "^5.0.1", "@types/marked": "^6.0.0", "@types/uuid": "^10.0.0", "@webcontainer/api": "^1.6.1", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-search": "^0.15.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "ai": "^4.3.15", "axios": "^1.9.0", "bcryptjs": "latest", "body-parser": "^2.2.0", "child_process": "latest", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "cors": "^2.8.5", "crypto-js": "^4.2.0", "d3": "latest", "date-fns": "latest", "dockerode": "^4.0.6", "embla-carousel-react": "8.5.1", "express": "^5.1.0", "framer-motion": "^12.10.4", "fs": "latest", "glob": "^11.0.2", "glob-promise": "^6.0.7", "html2canvas": "^1.4.1", "http-proxy-middleware": "^3.0.5", "immer": "^10.1.1", "input-otp": "1.4.1", "js-yaml": "^4.1.0", "jspdf": "^2.5.2", "katex": "latest", "lucide-react": "^0.454.0", "marked": "^15.0.12", "minimatch": "^10.0.1", "monaco-editor": "latest", "next": "15.2.4", "next-auth": "latest", "next-themes": "latest", "nextjs-toploader": "^3.8.16", "node-pty": "^1.0.0", "nodemailer": "latest", "os": "latest", "p-retry": "^6.2.1", "p-timeout": "^6.1.4", "path": "latest", "pdfjs-dist": "^5.2.133", "pg": "^8.16.0", "playwright-core": "^1.52.0", "prisma": "latest", "prismjs": "^1.30.0", "puppeteer-core": "^24.8.2", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-markdown": "latest", "react-resizable-panels": "^2.1.7", "react-syntax-highlighter": "latest", "react-vnc": "^3.1.0", "recharts": "latest", "rehype": "^13.0.2", "rehype-katex": "latest", "remark": "^15.0.1", "remark-gfm": "latest", "remark-html": "^16.0.1", "remark-math": "latest", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "ssh2": "^1.16.0", "stream": "^0.0.3", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "tar": "^7.4.3", "usehooks-ts": "^3.1.1", "uuid": "latest", "vaul": "^0.9.6", "weaviate-ts-client": "^2.2.0", "winston": "^3.17.0", "ws": "^8.18.2", "y-monaco": "latest", "y-websocket": "latest", "yjs": "latest", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.18", "@types/d3": "^7.4.3", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/node": "^22", "@types/node-fetch": "^2.6.12", "@types/pg": "^8.15.2", "@types/prismjs": "^1.26.5", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "@types/ws": "^8.18.1", "autoprefixer": "^10.4.20", "jest": "^29.7.0", "node-fetch": "^2.7.0", "postcss": "^8", "postcss-nested": "^7.0.2", "tailwindcss": "^3.4.17", "typescript": "^5"}, "description": "", "main": "lxd-client.js", "directories": {"doc": "docs", "lib": "lib"}, "repository": {"type": "git", "url": "git+https://github.com/Sa9eDesigns/app-gen.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/Sa9eDesigns/app-gen/issues"}, "homepage": "https://github.com/Sa9eDesigns/app-gen#readme", "packageManager": "pnpm@10.10.0"}