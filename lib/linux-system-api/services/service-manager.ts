/**
 * Service Manager
 * 
 * Manages system services using systemd or openrc
 */

import { EventEmitter } from 'events';
import { ServiceManager as ServiceManagerType, ServiceInfo, CommandExecutor } from '../types';

export class ServiceManager extends EventEmitter {
  private serviceManager: ServiceManagerType;
  private commandExecutor: CommandExecutor;

  constructor(serviceManager: ServiceManagerType, commandExecutor: CommandExecutor) {
    super();
    this.serviceManager = serviceManager;
    this.commandExecutor = commandExecutor;
  }

  /**
   * Get list of all services
   */
  public async getServiceList(): Promise<ServiceInfo[]> {
    let command: string;

    switch (this.serviceManager) {
      case 'systemd':
        command = 'systemctl list-units --type=service --no-pager --no-legend';
        break;
      case 'openrc':
        command = 'rc-status --servicelist';
        break;
      default:
        throw new Error(`Unsupported service manager: ${this.serviceManager}`);
    }

    try {
      const result = await this.commandExecutor.executeSimple(command);
      return this.parseServiceList(result.stdout);
    } catch (error) {
      console.error('Error getting service list:', error);
      return [];
    }
  }

  /**
   * Get service status
   */
  public async getServiceStatus(serviceName: string): Promise<ServiceInfo | null> {
    let command: string;

    switch (this.serviceManager) {
      case 'systemd':
        command = `systemctl status ${serviceName} --no-pager`;
        break;
      case 'openrc':
        command = `rc-service ${serviceName} status`;
        break;
      default:
        throw new Error(`Unsupported service manager: ${this.serviceManager}`);
    }

    try {
      const result = await this.commandExecutor.executeSimple(command);
      return this.parseServiceStatus(serviceName, result.stdout, result.stderr);
    } catch (error) {
      console.error(`Error getting service status for ${serviceName}:`, error);
      return null;
    }
  }

  /**
   * Start a service
   */
  public async startService(serviceName: string): Promise<void> {
    let command: string;

    switch (this.serviceManager) {
      case 'systemd':
        command = `systemctl start ${serviceName}`;
        break;
      case 'openrc':
        command = `rc-service ${serviceName} start`;
        break;
      default:
        throw new Error(`Unsupported service manager: ${this.serviceManager}`);
    }

    try {
      await this.commandExecutor.executeSimple(command, { sudo: true });
      this.emit('serviceStarted', { name: serviceName });
    } catch (error) {
      this.emit('serviceStartFailed', { name: serviceName, error });
      throw error;
    }
  }

  /**
   * Stop a service
   */
  public async stopService(serviceName: string): Promise<void> {
    let command: string;

    switch (this.serviceManager) {
      case 'systemd':
        command = `systemctl stop ${serviceName}`;
        break;
      case 'openrc':
        command = `rc-service ${serviceName} stop`;
        break;
      default:
        throw new Error(`Unsupported service manager: ${this.serviceManager}`);
    }

    try {
      await this.commandExecutor.executeSimple(command, { sudo: true });
      this.emit('serviceStopped', { name: serviceName });
    } catch (error) {
      this.emit('serviceStopFailed', { name: serviceName, error });
      throw error;
    }
  }

  /**
   * Restart a service
   */
  public async restartService(serviceName: string): Promise<void> {
    let command: string;

    switch (this.serviceManager) {
      case 'systemd':
        command = `systemctl restart ${serviceName}`;
        break;
      case 'openrc':
        command = `rc-service ${serviceName} restart`;
        break;
      default:
        throw new Error(`Unsupported service manager: ${this.serviceManager}`);
    }

    try {
      await this.commandExecutor.executeSimple(command, { sudo: true });
      this.emit('serviceRestarted', { name: serviceName });
    } catch (error) {
      this.emit('serviceRestartFailed', { name: serviceName, error });
      throw error;
    }
  }

  /**
   * Enable a service
   */
  public async enableService(serviceName: string): Promise<void> {
    let command: string;

    switch (this.serviceManager) {
      case 'systemd':
        command = `systemctl enable ${serviceName}`;
        break;
      case 'openrc':
        command = `rc-update add ${serviceName}`;
        break;
      default:
        throw new Error(`Unsupported service manager: ${this.serviceManager}`);
    }

    try {
      await this.commandExecutor.executeSimple(command, { sudo: true });
      this.emit('serviceEnabled', { name: serviceName });
    } catch (error) {
      this.emit('serviceEnableFailed', { name: serviceName, error });
      throw error;
    }
  }

  /**
   * Disable a service
   */
  public async disableService(serviceName: string): Promise<void> {
    let command: string;

    switch (this.serviceManager) {
      case 'systemd':
        command = `systemctl disable ${serviceName}`;
        break;
      case 'openrc':
        command = `rc-update del ${serviceName}`;
        break;
      default:
        throw new Error(`Unsupported service manager: ${this.serviceManager}`);
    }

    try {
      await this.commandExecutor.executeSimple(command, { sudo: true });
      this.emit('serviceDisabled', { name: serviceName });
    } catch (error) {
      this.emit('serviceDisableFailed', { name: serviceName, error });
      throw error;
    }
  }

  /**
   * Parse service list output
   */
  private parseServiceList(output: string): ServiceInfo[] {
    const services: ServiceInfo[] = [];
    const lines = output.split('\n').filter(line => line.trim());

    for (const line of lines) {
      if (this.serviceManager === 'systemd') {
        // systemd format: UNIT LOAD ACTIVE SUB DESCRIPTION
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 5) {
          const name = parts[0].replace('.service', '');
          const active = parts[2];
          const sub = parts[3];
          const description = parts.slice(4).join(' ');

          services.push({
            name,
            status: this.mapSystemdStatus(active, sub),
            enabled: false, // Would need separate command to check
            description,
          });
        }
      } else if (this.serviceManager === 'openrc') {
        // openrc format: service-name
        const serviceName = line.trim();
        if (serviceName) {
          services.push({
            name: serviceName,
            status: 'unknown',
            enabled: false,
            description: '',
          });
        }
      }
    }

    return services;
  }

  /**
   * Parse service status output
   */
  private parseServiceStatus(serviceName: string, stdout: string, stderr: string): ServiceInfo | null {
    if (this.serviceManager === 'systemd') {
      // Parse systemd status output
      const lines = stdout.split('\n');
      let status: 'active' | 'inactive' | 'failed' | 'unknown' = 'unknown';
      let enabled = false;
      let description = '';
      let mainPid: number | undefined;

      for (const line of lines) {
        if (line.includes('Active:')) {
          if (line.includes('active (running)')) {
            status = 'active';
          } else if (line.includes('inactive')) {
            status = 'inactive';
          } else if (line.includes('failed')) {
            status = 'failed';
          }
        } else if (line.includes('Loaded:')) {
          enabled = line.includes('enabled');
        } else if (line.includes('Main PID:')) {
          const pidMatch = line.match(/Main PID:\s*(\d+)/);
          if (pidMatch) {
            mainPid = parseInt(pidMatch[1]);
          }
        } else if (line.trim().startsWith('Description:')) {
          description = line.split('Description:')[1]?.trim() || '';
        }
      }

      return {
        name: serviceName,
        status,
        enabled,
        description,
        mainPid,
      };
    } else if (this.serviceManager === 'openrc') {
      // Parse openrc status output
      const status = stdout.includes('started') ? 'active' : 
                   stdout.includes('stopped') ? 'inactive' : 'unknown';

      return {
        name: serviceName,
        status: status as any,
        enabled: false, // Would need separate command
        description: '',
      };
    }

    return null;
  }

  /**
   * Map systemd status to our standard format
   */
  private mapSystemdStatus(active: string, sub: string): 'active' | 'inactive' | 'failed' | 'unknown' {
    if (active === 'active' && sub === 'running') {
      return 'active';
    } else if (active === 'inactive') {
      return 'inactive';
    } else if (active === 'failed') {
      return 'failed';
    }
    return 'unknown';
  }

  /**
   * Check if service is enabled
   */
  public async isServiceEnabled(serviceName: string): Promise<boolean> {
    let command: string;

    switch (this.serviceManager) {
      case 'systemd':
        command = `systemctl is-enabled ${serviceName}`;
        break;
      case 'openrc':
        command = `rc-update show | grep ${serviceName}`;
        break;
      default:
        return false;
    }

    try {
      const result = await this.commandExecutor.executeSimple(command);
      return result.exitCode === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if service is active/running
   */
  public async isServiceActive(serviceName: string): Promise<boolean> {
    let command: string;

    switch (this.serviceManager) {
      case 'systemd':
        command = `systemctl is-active ${serviceName}`;
        break;
      case 'openrc':
        command = `rc-service ${serviceName} status`;
        break;
      default:
        return false;
    }

    try {
      const result = await this.commandExecutor.executeSimple(command);
      if (this.serviceManager === 'systemd') {
        return result.stdout.trim() === 'active';
      } else {
        return result.stdout.includes('started');
      }
    } catch (error) {
      return false;
    }
  }

  /**
   * Get service logs
   */
  public async getServiceLogs(serviceName: string, lines = 50): Promise<string> {
    let command: string;

    switch (this.serviceManager) {
      case 'systemd':
        command = `journalctl -u ${serviceName} -n ${lines} --no-pager`;
        break;
      case 'openrc':
        // OpenRC doesn't have centralized logging, try common log locations
        command = `tail -n ${lines} /var/log/${serviceName}.log 2>/dev/null || tail -n ${lines} /var/log/messages | grep ${serviceName}`;
        break;
      default:
        throw new Error(`Unsupported service manager: ${this.serviceManager}`);
    }

    try {
      const result = await this.commandExecutor.executeSimple(command);
      return result.stdout;
    } catch (error) {
      console.error(`Error getting logs for service ${serviceName}:`, error);
      return '';
    }
  }
}
