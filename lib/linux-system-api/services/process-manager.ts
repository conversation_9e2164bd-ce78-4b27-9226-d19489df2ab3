/**
 * Process Manager
 * 
 * Manages system processes and monitoring
 */

import { EventEmitter } from 'events';
import { ProcessInfo, CommandExecutor } from '../types';

export class ProcessManager extends EventEmitter {
  private commandExecutor: CommandExecutor;

  constructor(commandExecutor: CommandExecutor) {
    super();
    this.commandExecutor = commandExecutor;
  }

  /**
   * Get list of running processes
   */
  public async getProcessList(): Promise<ProcessInfo[]> {
    try {
      const result = await this.commandExecutor.executeSimple('ps axo pid,ppid,user,comm,state,%cpu,%mem,lstart,cmd --no-headers');
      return this.parseProcessList(result.stdout);
    } catch (error) {
      console.error('Error getting process list:', error);
      return [];
    }
  }

  /**
   * Get process information by PID
   */
  public async getProcessInfo(pid: number): Promise<ProcessInfo | null> {
    try {
      const result = await this.commandExecutor.executeSimple(`ps -p ${pid} -o pid,ppid,user,comm,state,%cpu,%mem,lstart,cmd --no-headers`);
      const processes = this.parseProcessList(result.stdout);
      return processes.length > 0 ? processes[0] : null;
    } catch (error) {
      console.error(`Error getting process info for PID ${pid}:`, error);
      return null;
    }
  }

  /**
   * Kill a process by PID
   */
  public async killProcess(pid: number, signal = 'TERM'): Promise<void> {
    try {
      await this.commandExecutor.executeSimple(`kill -${signal} ${pid}`);
      this.emit('processKilled', { pid, signal });
    } catch (error) {
      this.emit('processKillFailed', { pid, signal, error });
      throw error;
    }
  }

  /**
   * Kill processes by name
   */
  public async killProcessByName(name: string, signal = 'TERM'): Promise<void> {
    try {
      await this.commandExecutor.executeSimple(`pkill -${signal} ${name}`);
      this.emit('processesKilled', { name, signal });
    } catch (error) {
      this.emit('processKillFailed', { name, signal, error });
      throw error;
    }
  }

  /**
   * Get processes by user
   */
  public async getProcessesByUser(username: string): Promise<ProcessInfo[]> {
    try {
      const result = await this.commandExecutor.executeSimple(`ps -u ${username} -o pid,ppid,user,comm,state,%cpu,%mem,lstart,cmd --no-headers`);
      return this.parseProcessList(result.stdout);
    } catch (error) {
      console.error(`Error getting processes for user ${username}:`, error);
      return [];
    }
  }

  /**
   * Get processes by name pattern
   */
  public async getProcessesByName(pattern: string): Promise<ProcessInfo[]> {
    try {
      const result = await this.commandExecutor.executeSimple(`pgrep -f "${pattern}" | xargs ps -o pid,ppid,user,comm,state,%cpu,%mem,lstart,cmd --no-headers -p`);
      return this.parseProcessList(result.stdout);
    } catch (error) {
      console.error(`Error getting processes matching ${pattern}:`, error);
      return [];
    }
  }

  /**
   * Get top processes by CPU usage
   */
  public async getTopProcessesByCpu(limit = 10): Promise<ProcessInfo[]> {
    try {
      const result = await this.commandExecutor.executeSimple(`ps axo pid,ppid,user,comm,state,%cpu,%mem,lstart,cmd --no-headers --sort=-%cpu | head -${limit}`);
      return this.parseProcessList(result.stdout);
    } catch (error) {
      console.error('Error getting top CPU processes:', error);
      return [];
    }
  }

  /**
   * Get top processes by memory usage
   */
  public async getTopProcessesByMemory(limit = 10): Promise<ProcessInfo[]> {
    try {
      const result = await this.commandExecutor.executeSimple(`ps axo pid,ppid,user,comm,state,%cpu,%mem,lstart,cmd --no-headers --sort=-%mem | head -${limit}`);
      return this.parseProcessList(result.stdout);
    } catch (error) {
      console.error('Error getting top memory processes:', error);
      return [];
    }
  }

  /**
   * Check if process exists
   */
  public async processExists(pid: number): Promise<boolean> {
    try {
      const result = await this.commandExecutor.executeSimple(`ps -p ${pid}`);
      return result.exitCode === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get process tree for a PID
   */
  public async getProcessTree(pid: number): Promise<ProcessInfo[]> {
    try {
      const result = await this.commandExecutor.executeSimple(`pstree -p ${pid} | grep -o '([0-9]*)' | tr -d '()' | xargs ps -o pid,ppid,user,comm,state,%cpu,%mem,lstart,cmd --no-headers -p`);
      return this.parseProcessList(result.stdout);
    } catch (error) {
      console.error(`Error getting process tree for PID ${pid}:`, error);
      return [];
    }
  }

  /**
   * Get system load average
   */
  public async getLoadAverage(): Promise<number[]> {
    try {
      const result = await this.commandExecutor.executeSimple('cat /proc/loadavg');
      const parts = result.stdout.trim().split(' ');
      return [
        parseFloat(parts[0]) || 0,
        parseFloat(parts[1]) || 0,
        parseFloat(parts[2]) || 0
      ];
    } catch (error) {
      console.error('Error getting load average:', error);
      return [0, 0, 0];
    }
  }

  /**
   * Get process count by state
   */
  public async getProcessCountByState(): Promise<{ [state: string]: number }> {
    try {
      const result = await this.commandExecutor.executeSimple('ps axo state --no-headers');
      const states = result.stdout.split('\n').filter(line => line.trim());
      const counts: { [state: string]: number } = {};

      for (const state of states) {
        const s = state.trim().charAt(0);
        counts[s] = (counts[s] || 0) + 1;
      }

      return counts;
    } catch (error) {
      console.error('Error getting process count by state:', error);
      return {};
    }
  }

  /**
   * Start a new process
   */
  public async startProcess(command: string, args: string[] = [], options: {
    cwd?: string;
    env?: Record<string, string>;
    background?: boolean;
  } = {}): Promise<number | null> {
    try {
      const fullCommand = `${command} ${args.join(' ')}`;
      const finalCommand = options.background ? `${fullCommand} &` : fullCommand;
      
      const result = await this.commandExecutor.executeSimple(finalCommand, {
        cwd: options.cwd,
        env: options.env
      });

      if (options.background) {
        // Try to get the PID of the background process
        const pidResult = await this.commandExecutor.executeSimple('echo $!');
        const pid = parseInt(pidResult.stdout.trim());
        if (!isNaN(pid)) {
          this.emit('processStarted', { pid, command: fullCommand });
          return pid;
        }
      }

      this.emit('processStarted', { command: fullCommand });
      return null;
    } catch (error) {
      this.emit('processStartFailed', { command, error });
      throw error;
    }
  }

  /**
   * Parse process list output
   */
  private parseProcessList(output: string): ProcessInfo[] {
    const processes: ProcessInfo[] = [];
    const lines = output.split('\n').filter(line => line.trim());

    for (const line of lines) {
      const parts = line.trim().split(/\s+/);
      if (parts.length >= 8) {
        const pid = parseInt(parts[0]);
        const ppid = parseInt(parts[1]);
        const user = parts[2];
        const name = parts[3];
        const state = parts[4];
        const cpu = parseFloat(parts[5]) || 0;
        const memory = parseFloat(parts[6]) || 0;
        const startTime = parts[7];
        const command = parts.slice(8).join(' ');

        if (!isNaN(pid)) {
          processes.push({
            pid,
            ppid: isNaN(ppid) ? 0 : ppid,
            name,
            command,
            user,
            state,
            cpu,
            memory,
            startTime
          });
        }
      }
    }

    return processes;
  }

  /**
   * Monitor process changes
   */
  public async startProcessMonitoring(interval = 5000): Promise<void> {
    const monitorProcess = async () => {
      try {
        const currentProcesses = await this.getProcessList();
        this.emit('processListUpdated', currentProcesses);
      } catch (error) {
        this.emit('processMonitoringError', error);
      }
    };

    // Initial scan
    await monitorProcess();

    // Set up periodic monitoring
    setInterval(monitorProcess, interval);
    this.emit('processMonitoringStarted', { interval });
  }

  /**
   * Get process resource usage
   */
  public async getProcessResourceUsage(pid: number): Promise<{
    cpu: number;
    memory: number;
    openFiles: number;
    threads: number;
  } | null> {
    try {
      // Get CPU and memory from ps
      const psResult = await this.commandExecutor.executeSimple(`ps -p ${pid} -o %cpu,%mem --no-headers`);
      const psData = psResult.stdout.trim().split(/\s+/);
      
      // Get open files count
      const lsofResult = await this.commandExecutor.executeSimple(`lsof -p ${pid} | wc -l`);
      const openFiles = parseInt(lsofResult.stdout.trim()) || 0;
      
      // Get thread count
      const threadsResult = await this.commandExecutor.executeSimple(`ps -p ${pid} -o nlwp --no-headers`);
      const threads = parseInt(threadsResult.stdout.trim()) || 0;

      return {
        cpu: parseFloat(psData[0]) || 0,
        memory: parseFloat(psData[1]) || 0,
        openFiles: Math.max(0, openFiles - 1), // Subtract header line
        threads
      };
    } catch (error) {
      console.error(`Error getting resource usage for PID ${pid}:`, error);
      return null;
    }
  }
}
