/**
 * Package Manager Service
 * 
 * Manages package operations for different Linux distributions
 */

import { EventEmitter } from 'events';
import { PackageManager as PackageManagerType, PackageInfo, CommandExecutor } from '../types';

export class PackageManager extends EventEmitter {
  private packageManager: PackageManagerType;
  private commandExecutor: CommandExecutor;

  constructor(packageManager: PackageManagerType, commandExecutor: CommandExecutor) {
    super();
    this.packageManager = packageManager;
    this.commandExecutor = commandExecutor;
  }

  /**
   * Install a package
   */
  public async installPackage(packageName: string, options: {
    version?: string;
    force?: boolean;
    noConfirm?: boolean;
  } = {}): Promise<void> {
    const { version, force, noConfirm } = options;
    let command: string;

    switch (this.packageManager) {
      case 'apt':
        command = 'apt-get install';
        if (noConfirm) command += ' -y';
        if (force) command += ' --force-yes';
        command += ` ${packageName}`;
        if (version) command += `=${version}`;
        break;

      case 'apk':
        command = 'apk add';
        if (force) command += ' --force';
        command += ` ${packageName}`;
        if (version) command += `=${version}`;
        break;

      default:
        throw new Error(`Unsupported package manager: ${this.packageManager}`);
    }

    try {
      await this.commandExecutor.executeSimple(command, { sudo: true });
      this.emit('packageInstalled', { name: packageName, version });
    } catch (error) {
      this.emit('packageInstallFailed', { name: packageName, error });
      throw error;
    }
  }

  /**
   * Remove a package
   */
  public async removePackage(packageName: string, options: {
    purge?: boolean;
    noConfirm?: boolean;
  } = {}): Promise<void> {
    const { purge, noConfirm } = options;
    let command: string;

    switch (this.packageManager) {
      case 'apt':
        command = purge ? 'apt-get purge' : 'apt-get remove';
        if (noConfirm) command += ' -y';
        command += ` ${packageName}`;
        break;

      case 'apk':
        command = `apk del ${packageName}`;
        break;

      default:
        throw new Error(`Unsupported package manager: ${this.packageManager}`);
    }

    try {
      await this.commandExecutor.executeSimple(command, { sudo: true });
      this.emit('packageRemoved', { name: packageName });
    } catch (error) {
      this.emit('packageRemoveFailed', { name: packageName, error });
      throw error;
    }
  }

  /**
   * Update package lists
   */
  public async updatePackageLists(): Promise<void> {
    let command: string;

    switch (this.packageManager) {
      case 'apt':
        command = 'apt-get update';
        break;

      case 'apk':
        command = 'apk update';
        break;

      default:
        throw new Error(`Unsupported package manager: ${this.packageManager}`);
    }

    try {
      await this.commandExecutor.executeSimple(command, { sudo: true });
      this.emit('packageListsUpdated');
    } catch (error) {
      this.emit('packageListsUpdateFailed', { error });
      throw error;
    }
  }

  /**
   * Upgrade all packages
   */
  public async upgradePackages(options: {
    noConfirm?: boolean;
  } = {}): Promise<void> {
    const { noConfirm } = options;
    let command: string;

    switch (this.packageManager) {
      case 'apt':
        command = 'apt-get upgrade';
        if (noConfirm) command += ' -y';
        break;

      case 'apk':
        command = 'apk upgrade';
        break;

      default:
        throw new Error(`Unsupported package manager: ${this.packageManager}`);
    }

    try {
      await this.commandExecutor.executeSimple(command, { sudo: true });
      this.emit('packagesUpgraded');
    } catch (error) {
      this.emit('packagesUpgradeFailed', { error });
      throw error;
    }
  }

  /**
   * Search for packages
   */
  public async searchPackages(query: string): Promise<PackageInfo[]> {
    let command: string;

    switch (this.packageManager) {
      case 'apt':
        command = `apt-cache search ${query}`;
        break;

      case 'apk':
        command = `apk search ${query}`;
        break;

      default:
        throw new Error(`Unsupported package manager: ${this.packageManager}`);
    }

    try {
      const result = await this.commandExecutor.executeSimple(command);
      return this.parseSearchResults(result.stdout);
    } catch (error) {
      console.error('Error searching packages:', error);
      return [];
    }
  }

  /**
   * Get installed packages
   */
  public async getInstalledPackages(): Promise<PackageInfo[]> {
    let command: string;

    switch (this.packageManager) {
      case 'apt':
        command = 'dpkg-query -W -f="${Package} ${Version} ${Installed-Size} ${Description}\\n"';
        break;

      case 'apk':
        command = 'apk info -v';
        break;

      default:
        throw new Error(`Unsupported package manager: ${this.packageManager}`);
    }

    try {
      const result = await this.commandExecutor.executeSimple(command);
      return this.parseInstalledPackages(result.stdout);
    } catch (error) {
      console.error('Error getting installed packages:', error);
      return [];
    }
  }

  /**
   * Get package information
   */
  public async getPackageInfo(packageName: string): Promise<PackageInfo | null> {
    let command: string;

    switch (this.packageManager) {
      case 'apt':
        command = `apt-cache show ${packageName}`;
        break;

      case 'apk':
        command = `apk info ${packageName}`;
        break;

      default:
        throw new Error(`Unsupported package manager: ${this.packageManager}`);
    }

    try {
      const result = await this.commandExecutor.executeSimple(command);
      return this.parsePackageInfo(result.stdout, packageName);
    } catch (error) {
      console.error('Error getting package info:', error);
      return null;
    }
  }

  /**
   * Check if package is installed
   */
  public async isPackageInstalled(packageName: string): Promise<boolean> {
    let command: string;

    switch (this.packageManager) {
      case 'apt':
        command = `dpkg -l ${packageName}`;
        break;

      case 'apk':
        command = `apk info -e ${packageName}`;
        break;

      default:
        throw new Error(`Unsupported package manager: ${this.packageManager}`);
    }

    try {
      const result = await this.commandExecutor.executeSimple(command);
      return result.exitCode === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get upgradable packages
   */
  public async getUpgradablePackages(): Promise<PackageInfo[]> {
    let command: string;

    switch (this.packageManager) {
      case 'apt':
        command = 'apt list --upgradable';
        break;

      case 'apk':
        // Alpine doesn't have a direct equivalent, so we'll check for updates
        command = 'apk version -l "<"';
        break;

      default:
        throw new Error(`Unsupported package manager: ${this.packageManager}`);
    }

    try {
      const result = await this.commandExecutor.executeSimple(command);
      return this.parseUpgradablePackages(result.stdout);
    } catch (error) {
      console.error('Error getting upgradable packages:', error);
      return [];
    }
  }

  /**
   * Parse search results
   */
  private parseSearchResults(output: string): PackageInfo[] {
    const packages: PackageInfo[] = [];
    const lines = output.split('\n').filter(line => line.trim());

    for (const line of lines) {
      if (this.packageManager === 'apt') {
        // Format: package-name - description
        const match = line.match(/^(\S+)\s+-\s+(.+)$/);
        if (match) {
          packages.push({
            name: match[1],
            version: 'unknown',
            description: match[2],
            size: 0,
            installed: false,
            upgradable: false
          });
        }
      } else if (this.packageManager === 'apk') {
        // Format: package-name-version description
        const parts = line.split(' ');
        if (parts.length >= 2) {
          const nameVersion = parts[0];
          const description = parts.slice(1).join(' ');
          const lastDash = nameVersion.lastIndexOf('-');
          const name = lastDash > 0 ? nameVersion.substring(0, lastDash) : nameVersion;
          const version = lastDash > 0 ? nameVersion.substring(lastDash + 1) : 'unknown';

          packages.push({
            name,
            version,
            description,
            size: 0,
            installed: false,
            upgradable: false
          });
        }
      }
    }

    return packages;
  }

  /**
   * Parse installed packages
   */
  private parseInstalledPackages(output: string): PackageInfo[] {
    const packages: PackageInfo[] = [];
    const lines = output.split('\n').filter(line => line.trim());

    for (const line of lines) {
      if (this.packageManager === 'apt') {
        // Format: package version size description
        const parts = line.split(' ');
        if (parts.length >= 4) {
          packages.push({
            name: parts[0],
            version: parts[1],
            description: parts.slice(3).join(' '),
            size: parseInt(parts[2]) || 0,
            installed: true,
            upgradable: false
          });
        }
      } else if (this.packageManager === 'apk') {
        // Format: package-name-version
        const nameVersion = line.trim();
        const lastDash = nameVersion.lastIndexOf('-');
        const name = lastDash > 0 ? nameVersion.substring(0, lastDash) : nameVersion;
        const version = lastDash > 0 ? nameVersion.substring(lastDash + 1) : 'unknown';

        packages.push({
          name,
          version,
          description: '',
          size: 0,
          installed: true,
          upgradable: false
        });
      }
    }

    return packages;
  }

  /**
   * Parse package info
   */
  private parsePackageInfo(output: string, packageName: string): PackageInfo | null {
    const lines = output.split('\n');
    let version = 'unknown';
    let description = '';
    let size = 0;

    for (const line of lines) {
      if (line.startsWith('Version:')) {
        version = line.split(':')[1]?.trim() || 'unknown';
      } else if (line.startsWith('Description:')) {
        description = line.split(':')[1]?.trim() || '';
      } else if (line.startsWith('Size:') || line.startsWith('Installed-Size:')) {
        const sizeStr = line.split(':')[1]?.trim();
        size = parseInt(sizeStr || '0') || 0;
      }
    }

    return {
      name: packageName,
      version,
      description,
      size,
      installed: false, // Will be determined by caller
      upgradable: false
    };
  }

  /**
   * Parse upgradable packages
   */
  private parseUpgradablePackages(output: string): PackageInfo[] {
    const packages: PackageInfo[] = [];
    const lines = output.split('\n').filter(line => line.trim());

    for (const line of lines) {
      if (this.packageManager === 'apt') {
        // Format: package/suite version [arch] [upgradable from: old-version]
        const match = line.match(/^(\S+)\/\S+\s+(\S+)\s+.*upgradable from:\s+(\S+)/);
        if (match) {
          packages.push({
            name: match[1],
            version: match[3], // Current version
            newVersion: match[2], // New version
            description: '',
            size: 0,
            installed: true,
            upgradable: true
          });
        }
      } else if (this.packageManager === 'apk') {
        // Alpine format varies, basic parsing
        const parts = line.split(' ');
        if (parts.length >= 1) {
          const nameVersion = parts[0];
          const lastDash = nameVersion.lastIndexOf('-');
          const name = lastDash > 0 ? nameVersion.substring(0, lastDash) : nameVersion;
          const version = lastDash > 0 ? nameVersion.substring(lastDash + 1) : 'unknown';

          packages.push({
            name,
            version,
            description: '',
            size: 0,
            installed: true,
            upgradable: true
          });
        }
      }
    }

    return packages;
  }
}
