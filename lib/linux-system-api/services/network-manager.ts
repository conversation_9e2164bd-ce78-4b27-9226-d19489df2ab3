/**
 * Network Manager
 * 
 * Manages network interfaces and configuration
 */

import { EventEmitter } from 'events';
import { NetworkInterface, NetworkConfiguration, CommandExecutor } from '../types';

export class NetworkManager extends EventEmitter {
  private commandExecutor: CommandExecutor;

  constructor(commandExecutor: CommandExecutor) {
    super();
    this.commandExecutor = commandExecutor;
  }

  /**
   * Get network interface information
   */
  public async getNetworkInterfaces(): Promise<NetworkInterface[]> {
    try {
      const interfaces: NetworkInterface[] = [];
      
      // Get interface statistics from /proc/net/dev
      const devResult = await this.commandExecutor.executeSimple('cat /proc/net/dev');
      const devLines = devResult.stdout.split('\n').slice(2); // Skip headers
      
      for (const line of devLines) {
        if (!line.trim()) continue;
        
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 17) {
          const name = parts[0].replace(':', '');
          const rxBytes = parseInt(parts[1]) || 0;
          const rxPackets = parseInt(parts[2]) || 0;
          const txBytes = parseInt(parts[9]) || 0;
          const txPackets = parseInt(parts[10]) || 0;

          // Get additional interface info
          const interfaceInfo = await this.getInterfaceDetails(name);
          
          interfaces.push({
            name,
            type: this.getInterfaceType(name),
            state: interfaceInfo.state,
            ipv4: interfaceInfo.ipv4,
            ipv6: interfaceInfo.ipv6,
            mac: interfaceInfo.mac,
            rxBytes,
            txBytes,
            rxPackets,
            txPackets
          });
        }
      }

      return interfaces;
    } catch (error) {
      console.error('Error getting network interfaces:', error);
      return [];
    }
  }

  /**
   * Get detailed interface information
   */
  private async getInterfaceDetails(interfaceName: string): Promise<{
    state: 'up' | 'down';
    ipv4?: string;
    ipv6?: string;
    mac: string;
  }> {
    try {
      // Get interface info using ip command
      const result = await this.commandExecutor.executeSimple(`ip addr show ${interfaceName}`);
      const output = result.stdout;
      
      let state: 'up' | 'down' = 'down';
      let ipv4: string | undefined;
      let ipv6: string | undefined;
      let mac = 'unknown';

      // Parse state
      if (output.includes('state UP')) {
        state = 'up';
      }

      // Parse MAC address
      const macMatch = output.match(/link\/ether\s+([a-f0-9:]{17})/);
      if (macMatch) {
        mac = macMatch[1];
      }

      // Parse IPv4 address
      const ipv4Match = output.match(/inet\s+([0-9.]+\/[0-9]+)/);
      if (ipv4Match) {
        ipv4 = ipv4Match[1].split('/')[0];
      }

      // Parse IPv6 address
      const ipv6Match = output.match(/inet6\s+([a-f0-9:]+\/[0-9]+)/);
      if (ipv6Match && !ipv6Match[1].startsWith('fe80')) { // Skip link-local
        ipv6 = ipv6Match[1].split('/')[0];
      }

      return { state, ipv4, ipv6, mac };
    } catch (error) {
      return { state: 'down', mac: 'unknown' };
    }
  }

  /**
   * Determine interface type based on name
   */
  private getInterfaceType(name: string): string {
    if (name.startsWith('eth')) return 'ethernet';
    if (name.startsWith('wlan') || name.startsWith('wifi')) return 'wireless';
    if (name.startsWith('lo')) return 'loopback';
    if (name.startsWith('docker') || name.startsWith('br-')) return 'bridge';
    if (name.startsWith('tun') || name.startsWith('tap')) return 'tunnel';
    return 'unknown';
  }

  /**
   * Configure network interface
   */
  public async configureInterface(config: NetworkConfiguration): Promise<void> {
    try {
      const { interface: iface, type, ipv4, netmask, gateway, dns } = config;

      if (type === 'static' && ipv4 && netmask) {
        // Set static IP
        await this.commandExecutor.executeSimple(`ip addr add ${ipv4}/${this.cidrFromNetmask(netmask)} dev ${iface}`, { sudo: true });
        
        // Set gateway if provided
        if (gateway) {
          await this.commandExecutor.executeSimple(`ip route add default via ${gateway}`, { sudo: true });
        }
        
        // Set DNS if provided
        if (dns && dns.length > 0) {
          const dnsConfig = dns.map(server => `nameserver ${server}`).join('\n');
          await this.commandExecutor.executeSimple(`echo "${dnsConfig}" > /etc/resolv.conf`, { sudo: true });
        }
      } else if (type === 'dhcp') {
        // Use DHCP
        await this.commandExecutor.executeSimple(`dhclient ${iface}`, { sudo: true });
      }

      this.emit('interfaceConfigured', config);
    } catch (error) {
      this.emit('interfaceConfigurationFailed', { config, error });
      throw error;
    }
  }

  /**
   * Bring interface up
   */
  public async bringInterfaceUp(interfaceName: string): Promise<void> {
    try {
      await this.commandExecutor.executeSimple(`ip link set ${interfaceName} up`, { sudo: true });
      this.emit('interfaceUp', { interface: interfaceName });
    } catch (error) {
      this.emit('interfaceOperationFailed', { interface: interfaceName, operation: 'up', error });
      throw error;
    }
  }

  /**
   * Bring interface down
   */
  public async bringInterfaceDown(interfaceName: string): Promise<void> {
    try {
      await this.commandExecutor.executeSimple(`ip link set ${interfaceName} down`, { sudo: true });
      this.emit('interfaceDown', { interface: interfaceName });
    } catch (error) {
      this.emit('interfaceOperationFailed', { interface: interfaceName, operation: 'down', error });
      throw error;
    }
  }

  /**
   * Get routing table
   */
  public async getRoutingTable(): Promise<any[]> {
    try {
      const result = await this.commandExecutor.executeSimple('ip route show');
      return this.parseRoutingTable(result.stdout);
    } catch (error) {
      console.error('Error getting routing table:', error);
      return [];
    }
  }

  /**
   * Add route
   */
  public async addRoute(destination: string, gateway: string, interface?: string): Promise<void> {
    try {
      let command = `ip route add ${destination} via ${gateway}`;
      if (interface) {
        command += ` dev ${interface}`;
      }
      
      await this.commandExecutor.executeSimple(command, { sudo: true });
      this.emit('routeAdded', { destination, gateway, interface });
    } catch (error) {
      this.emit('routeOperationFailed', { operation: 'add', destination, gateway, error });
      throw error;
    }
  }

  /**
   * Delete route
   */
  public async deleteRoute(destination: string, gateway?: string): Promise<void> {
    try {
      let command = `ip route del ${destination}`;
      if (gateway) {
        command += ` via ${gateway}`;
      }
      
      await this.commandExecutor.executeSimple(command, { sudo: true });
      this.emit('routeDeleted', { destination, gateway });
    } catch (error) {
      this.emit('routeOperationFailed', { operation: 'delete', destination, gateway, error });
      throw error;
    }
  }

  /**
   * Get network connections
   */
  public async getNetworkConnections(): Promise<any[]> {
    try {
      const result = await this.commandExecutor.executeSimple('ss -tuln');
      return this.parseNetworkConnections(result.stdout);
    } catch (error) {
      console.error('Error getting network connections:', error);
      return [];
    }
  }

  /**
   * Test network connectivity
   */
  public async pingHost(host: string, count = 4): Promise<{
    success: boolean;
    packetsTransmitted: number;
    packetsReceived: number;
    packetLoss: number;
    avgTime?: number;
  }> {
    try {
      const result = await this.commandExecutor.executeSimple(`ping -c ${count} ${host}`);
      return this.parsePingResult(result.stdout);
    } catch (error) {
      return {
        success: false,
        packetsTransmitted: count,
        packetsReceived: 0,
        packetLoss: 100
      };
    }
  }

  /**
   * Get DNS configuration
   */
  public async getDnsConfiguration(): Promise<string[]> {
    try {
      const result = await this.commandExecutor.executeSimple('cat /etc/resolv.conf');
      const lines = result.stdout.split('\n');
      const dnsServers: string[] = [];
      
      for (const line of lines) {
        if (line.startsWith('nameserver')) {
          const server = line.split(/\s+/)[1];
          if (server) {
            dnsServers.push(server);
          }
        }
      }
      
      return dnsServers;
    } catch (error) {
      console.error('Error getting DNS configuration:', error);
      return [];
    }
  }

  /**
   * Set DNS servers
   */
  public async setDnsServers(servers: string[]): Promise<void> {
    try {
      const dnsConfig = servers.map(server => `nameserver ${server}`).join('\n');
      await this.commandExecutor.executeSimple(`echo "${dnsConfig}" > /etc/resolv.conf`, { sudo: true });
      this.emit('dnsConfigured', { servers });
    } catch (error) {
      this.emit('dnsConfigurationFailed', { servers, error });
      throw error;
    }
  }

  /**
   * Convert netmask to CIDR notation
   */
  private cidrFromNetmask(netmask: string): number {
    const parts = netmask.split('.').map(Number);
    let cidr = 0;
    
    for (const part of parts) {
      cidr += part.toString(2).split('1').length - 1;
    }
    
    return cidr;
  }

  /**
   * Parse routing table output
   */
  private parseRoutingTable(output: string): any[] {
    const routes: any[] = [];
    const lines = output.split('\n').filter(line => line.trim());
    
    for (const line of lines) {
      const parts = line.trim().split(/\s+/);
      if (parts.length >= 3) {
        routes.push({
          destination: parts[0],
          gateway: parts.includes('via') ? parts[parts.indexOf('via') + 1] : null,
          interface: parts.includes('dev') ? parts[parts.indexOf('dev') + 1] : null,
          metric: parts.includes('metric') ? parts[parts.indexOf('metric') + 1] : null
        });
      }
    }
    
    return routes;
  }

  /**
   * Parse network connections output
   */
  private parseNetworkConnections(output: string): any[] {
    const connections: any[] = [];
    const lines = output.split('\n').slice(1); // Skip header
    
    for (const line of lines) {
      if (!line.trim()) continue;
      
      const parts = line.trim().split(/\s+/);
      if (parts.length >= 5) {
        connections.push({
          protocol: parts[0],
          state: parts[1],
          localAddress: parts[4],
          remoteAddress: parts[5] || '*:*'
        });
      }
    }
    
    return connections;
  }

  /**
   * Parse ping result
   */
  private parsePingResult(output: string): {
    success: boolean;
    packetsTransmitted: number;
    packetsReceived: number;
    packetLoss: number;
    avgTime?: number;
  } {
    const lines = output.split('\n');
    let packetsTransmitted = 0;
    let packetsReceived = 0;
    let packetLoss = 100;
    let avgTime: number | undefined;
    
    for (const line of lines) {
      // Parse packet statistics
      const statsMatch = line.match(/(\d+) packets transmitted, (\d+) received, (\d+)% packet loss/);
      if (statsMatch) {
        packetsTransmitted = parseInt(statsMatch[1]);
        packetsReceived = parseInt(statsMatch[2]);
        packetLoss = parseInt(statsMatch[3]);
      }
      
      // Parse timing statistics
      const timeMatch = line.match(/min\/avg\/max\/mdev = [0-9.]+\/([0-9.]+)\/[0-9.]+\/[0-9.]+ ms/);
      if (timeMatch) {
        avgTime = parseFloat(timeMatch[1]);
      }
    }
    
    return {
      success: packetsReceived > 0,
      packetsTransmitted,
      packetsReceived,
      packetLoss,
      avgTime
    };
  }
}
