/**
 * Filesystem Manager
 * 
 * Manages file system operations with security validation
 */

import { EventEmitter } from 'events';
import { FileSystemOperation, CommandExecutor } from '../types';

export class FilesystemManager extends EventEmitter {
  private commandExecutor: CommandExecutor;

  constructor(commandExecutor: CommandExecutor) {
    super();
    this.commandExecutor = commandExecutor;
  }

  /**
   * Read file contents
   */
  public async readFile(path: string): Promise<string> {
    try {
      const result = await this.commandExecutor.executeSimple(`cat "${path}"`);
      return result.stdout;
    } catch (error) {
      this.emit('fileOperationFailed', { operation: 'read', path, error });
      throw error;
    }
  }

  /**
   * Write file contents
   */
  public async writeFile(path: string, content: string): Promise<void> {
    try {
      // Use echo with proper escaping
      const escapedContent = content.replace(/"/g, '\\"');
      await this.commandExecutor.executeSimple(`echo "${escapedContent}" > "${path}"`);
      this.emit('fileWritten', { path });
    } catch (error) {
      this.emit('fileOperationFailed', { operation: 'write', path, error });
      throw error;
    }
  }

  /**
   * Delete file or directory
   */
  public async delete(path: string, recursive = false): Promise<void> {
    try {
      const command = recursive ? `rm -rf "${path}"` : `rm "${path}"`;
      await this.commandExecutor.executeSimple(command);
      this.emit('fileDeleted', { path, recursive });
    } catch (error) {
      this.emit('fileOperationFailed', { operation: 'delete', path, error });
      throw error;
    }
  }

  /**
   * Copy file or directory
   */
  public async copy(source: string, destination: string, recursive = false): Promise<void> {
    try {
      const command = recursive ? `cp -r "${source}" "${destination}"` : `cp "${source}" "${destination}"`;
      await this.commandExecutor.executeSimple(command);
      this.emit('fileCopied', { source, destination, recursive });
    } catch (error) {
      this.emit('fileOperationFailed', { operation: 'copy', source, destination, error });
      throw error;
    }
  }

  /**
   * Move file or directory
   */
  public async move(source: string, destination: string): Promise<void> {
    try {
      await this.commandExecutor.executeSimple(`mv "${source}" "${destination}"`);
      this.emit('fileMoved', { source, destination });
    } catch (error) {
      this.emit('fileOperationFailed', { operation: 'move', source, destination, error });
      throw error;
    }
  }

  /**
   * Create directory
   */
  public async createDirectory(path: string, recursive = false): Promise<void> {
    try {
      const command = recursive ? `mkdir -p "${path}"` : `mkdir "${path}"`;
      await this.commandExecutor.executeSimple(command);
      this.emit('directoryCreated', { path, recursive });
    } catch (error) {
      this.emit('fileOperationFailed', { operation: 'mkdir', path, error });
      throw error;
    }
  }

  /**
   * List directory contents
   */
  public async listDirectory(path: string, detailed = false): Promise<any[]> {
    try {
      const command = detailed ? `ls -la "${path}"` : `ls -1 "${path}"`;
      const result = await this.commandExecutor.executeSimple(command);
      return this.parseDirectoryListing(result.stdout, detailed);
    } catch (error) {
      this.emit('fileOperationFailed', { operation: 'list', path, error });
      throw error;
    }
  }

  /**
   * Get file/directory information
   */
  public async getFileInfo(path: string): Promise<any> {
    try {
      const result = await this.commandExecutor.executeSimple(`stat "${path}"`);
      return this.parseStatOutput(result.stdout);
    } catch (error) {
      this.emit('fileOperationFailed', { operation: 'stat', path, error });
      throw error;
    }
  }

  /**
   * Change file permissions
   */
  public async changePermissions(path: string, mode: string, recursive = false): Promise<void> {
    try {
      const command = recursive ? `chmod -R ${mode} "${path}"` : `chmod ${mode} "${path}"`;
      await this.commandExecutor.executeSimple(command);
      this.emit('permissionsChanged', { path, mode, recursive });
    } catch (error) {
      this.emit('fileOperationFailed', { operation: 'chmod', path, error });
      throw error;
    }
  }

  /**
   * Change file ownership
   */
  public async changeOwnership(path: string, owner: string, group?: string, recursive = false): Promise<void> {
    try {
      const ownerGroup = group ? `${owner}:${group}` : owner;
      const command = recursive ? `chown -R ${ownerGroup} "${path}"` : `chown ${ownerGroup} "${path}"`;
      await this.commandExecutor.executeSimple(command, { sudo: true });
      this.emit('ownershipChanged', { path, owner, group, recursive });
    } catch (error) {
      this.emit('fileOperationFailed', { operation: 'chown', path, error });
      throw error;
    }
  }

  /**
   * Check if file/directory exists
   */
  public async exists(path: string): Promise<boolean> {
    try {
      const result = await this.commandExecutor.executeSimple(`test -e "${path}"`);
      return result.exitCode === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if path is a file
   */
  public async isFile(path: string): Promise<boolean> {
    try {
      const result = await this.commandExecutor.executeSimple(`test -f "${path}"`);
      return result.exitCode === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if path is a directory
   */
  public async isDirectory(path: string): Promise<boolean> {
    try {
      const result = await this.commandExecutor.executeSimple(`test -d "${path}"`);
      return result.exitCode === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Find files matching pattern
   */
  public async findFiles(basePath: string, pattern: string, maxDepth?: number): Promise<string[]> {
    try {
      let command = `find "${basePath}" -name "${pattern}"`;
      if (maxDepth !== undefined) {
        command += ` -maxdepth ${maxDepth}`;
      }
      
      const result = await this.commandExecutor.executeSimple(command);
      return result.stdout.split('\n').filter(line => line.trim());
    } catch (error) {
      this.emit('fileOperationFailed', { operation: 'find', basePath, pattern, error });
      throw error;
    }
  }

  /**
   * Get disk usage for path
   */
  public async getDiskUsage(path: string): Promise<{ size: number; used: number; available: number }> {
    try {
      const result = await this.commandExecutor.executeSimple(`du -sb "${path}"`);
      const size = parseInt(result.stdout.split('\t')[0]) || 0;
      
      // Get available space for the filesystem containing this path
      const dfResult = await this.commandExecutor.executeSimple(`df -B1 "${path}"`);
      const dfLines = dfResult.stdout.split('\n');
      if (dfLines.length > 1) {
        const parts = dfLines[1].trim().split(/\s+/);
        const total = parseInt(parts[1]) || 0;
        const used = parseInt(parts[2]) || 0;
        const available = parseInt(parts[3]) || 0;
        
        return { size: total, used, available };
      }
      
      return { size, used: size, available: 0 };
    } catch (error) {
      this.emit('fileOperationFailed', { operation: 'du', path, error });
      throw error;
    }
  }

  /**
   * Parse directory listing output
   */
  private parseDirectoryListing(output: string, detailed: boolean): any[] {
    const lines = output.split('\n').filter(line => line.trim());
    const items: any[] = [];

    for (const line of lines) {
      if (detailed) {
        // Parse ls -la output
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 9) {
          const permissions = parts[0];
          const links = parts[1];
          const owner = parts[2];
          const group = parts[3];
          const size = parseInt(parts[4]) || 0;
          const name = parts.slice(8).join(' ');
          
          // Skip . and .. entries
          if (name === '.' || name === '..') continue;
          
          items.push({
            name,
            permissions,
            owner,
            group,
            size,
            links: parseInt(links) || 0,
            isDirectory: permissions.startsWith('d'),
            isFile: permissions.startsWith('-'),
            isSymlink: permissions.startsWith('l')
          });
        }
      } else {
        // Simple listing
        if (line.trim()) {
          items.push({
            name: line.trim(),
            isDirectory: false, // Would need additional check
            isFile: true
          });
        }
      }
    }

    return items;
  }

  /**
   * Parse stat command output
   */
  private parseStatOutput(output: string): any {
    const info: any = {};
    const lines = output.split('\n');

    for (const line of lines) {
      if (line.includes('File:')) {
        const match = line.match(/File:\s*(.+)/);
        if (match) info.name = match[1].trim();
      } else if (line.includes('Size:')) {
        const match = line.match(/Size:\s*(\d+)/);
        if (match) info.size = parseInt(match[1]);
      } else if (line.includes('Access:') && line.includes('(')) {
        const match = line.match(/Access:\s*\((\d+)\/([^)]+)\)/);
        if (match) {
          info.mode = match[1];
          info.permissions = match[2];
        }
      } else if (line.includes('Uid:')) {
        const match = line.match(/Uid:\s*\(\s*(\d+)\/\s*([^)]+)\)/);
        if (match) {
          info.uid = parseInt(match[1]);
          info.owner = match[2].trim();
        }
      } else if (line.includes('Gid:')) {
        const match = line.match(/Gid:\s*\(\s*(\d+)\/\s*([^)]+)\)/);
        if (match) {
          info.gid = parseInt(match[1]);
          info.group = match[2].trim();
        }
      }
    }

    return info;
  }

  /**
   * Execute filesystem operation with validation
   */
  public async executeOperation(operation: FileSystemOperation): Promise<any> {
    switch (operation.type) {
      case 'read':
        return this.readFile(operation.path);
      
      case 'write':
        if (!operation.data) throw new Error('Data is required for write operation');
        return this.writeFile(operation.path, operation.data.toString());
      
      case 'delete':
        return this.delete(operation.path, operation.recursive);
      
      case 'copy':
        if (!operation.destination) throw new Error('Destination is required for copy operation');
        return this.copy(operation.path, operation.destination, operation.recursive);
      
      case 'move':
        if (!operation.destination) throw new Error('Destination is required for move operation');
        return this.move(operation.path, operation.destination);
      
      case 'mkdir':
        return this.createDirectory(operation.path, operation.recursive);
      
      case 'chmod':
        if (!operation.mode) throw new Error('Mode is required for chmod operation');
        return this.changePermissions(operation.path, operation.mode, operation.recursive);
      
      case 'chown':
        if (!operation.owner) throw new Error('Owner is required for chown operation');
        return this.changeOwnership(operation.path, operation.owner, operation.group, operation.recursive);
      
      default:
        throw new Error(`Unsupported filesystem operation: ${operation.type}`);
    }
  }
}
