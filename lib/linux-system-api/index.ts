/**
 * Linux System API
 * 
 * Comprehensive API system for Ubuntu and Alpine Linux with WebSocket support
 * Provides programmatic access to system operations, monitoring, and management
 */

// Core exports
export * from './core/system-manager';
export * from './core/websocket-server';
export * from './core/system-monitor';

// Services
export * from './services/package-manager';
export * from './services/service-manager';
export * from './services/filesystem-manager';
export * from './services/process-manager';
export * from './services/network-manager';

// API clients
export * from './api/system-client';
export * from './api/websocket-client';

// Types
export * from './types';

// Utils
export * from './utils/system-detector';
export * from './utils/command-executor';
export * from './utils/security-validator';

// Hooks
export * from './hooks/use-linux-system';
export * from './hooks/use-system-monitor';
export * from './hooks/use-system-websocket';
