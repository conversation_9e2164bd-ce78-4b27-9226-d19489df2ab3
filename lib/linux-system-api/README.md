# Linux System API

A comprehensive API system for Ubuntu and Alpine Linux that provides programmatic access via WebSockets and REST endpoints. This system allows for real-time system monitoring, command execution, package management, service control, and more.

## Features

### 🔧 Core Capabilities
- **System Information**: Get comprehensive system details including distribution, hardware, and performance metrics
- **Command Execution**: Secure command execution with validation and streaming output
- **Package Management**: Install, remove, search, and manage packages (apt/apk)
- **Service Management**: Control systemd/openrc services
- **Process Management**: Monitor and control system processes
- **Network Management**: Configure network interfaces and monitor connections
- **File System Operations**: Secure file and directory operations
- **Real-time Monitoring**: WebSocket-based real-time system metrics

### 🛡️ Security Features
- Command validation and sanitization
- Blocked command patterns
- Path access control
- User permission validation
- Audit logging
- Rate limiting

### 🌐 Communication Methods
- **REST API**: Traditional HTTP endpoints for system operations
- **WebSocket**: Real-time bidirectional communication
- **Server-Sent Events**: Streaming command output

## Architecture

```
lib/linux-system-api/
├── core/
│   ├── system-manager.ts          # Main system manager
│   ├── websocket-server.ts        # WebSocket server implementation
│   └── system-monitor.ts          # Real-time system monitoring
├── services/
│   ├── package-manager.ts         # Package management (apt/apk)
│   ├── service-manager.ts         # Service control (systemd/openrc)
│   ├── filesystem-manager.ts      # File system operations
│   ├── process-manager.ts         # Process management
│   └── network-manager.ts         # Network configuration
├── utils/
│   ├── system-detector.ts         # Distribution detection
│   ├── command-executor.ts        # Secure command execution
│   └── security-validator.ts      # Security validation
├── hooks/
│   └── use-linux-system.ts        # React hook for easy integration
├── types/
│   └── index.ts                   # TypeScript type definitions
└── index.ts                       # Main exports
```

## Quick Start

### 1. Installation

The Linux System API is already integrated into your project. No additional installation required.

### 2. Basic Usage

#### React Hook (Recommended)

```tsx
import { useLinuxSystem } from '@/lib/linux-system-api/hooks/use-linux-system';

function SystemDashboard() {
  const {
    systemInfo,
    isLoading,
    error,
    executeCommand,
    getInstalledPackages,
    installPackage
  } = useLinuxSystem({ 
    autoInitialize: true, 
    enableWebSocket: true 
  });

  const handleInstallPackage = async () => {
    try {
      await installPackage('htop');
      console.log('Package installed successfully');
    } catch (err) {
      console.error('Installation failed:', err);
    }
  };

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h1>System: {systemInfo?.hostname}</h1>
      <p>Distribution: {systemInfo?.distribution} {systemInfo?.version}</p>
      <button onClick={handleInstallPackage}>Install htop</button>
    </div>
  );
}
```

#### Direct API Usage

```typescript
import { SystemManager } from '@/lib/linux-system-api';

// Initialize system manager
const systemManager = SystemManager.getInstance({
  enableMonitoring: true,
  enableWebSocket: true,
  webSocketPort: 3002
});

await systemManager.initialize();

// Get system information
const systemInfo = await systemManager.getSystemInfo();
console.log('System:', systemInfo);

// Execute command
const result = await systemManager.executeCommand('ls -la /home');
console.log('Command output:', result.stdout);

// Package management
const packageManager = systemManager.getPackageManager();
await packageManager.installPackage('nginx');
```

### 3. WebSocket Connection

```javascript
const ws = new WebSocket('ws://localhost:3002/linux-system-ws');

ws.onopen = () => {
  // Subscribe to system events
  ws.send(JSON.stringify({
    type: 'subscribe',
    data: {
      events: ['system_metrics', 'package_events', 'command_output']
    },
    timestamp: new Date().toISOString()
  }));
};

ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  console.log('Received:', message);
};

// Execute command via WebSocket
ws.send(JSON.stringify({
  type: 'command',
  data: {
    command: 'top',
    args: ['-n', '1']
  },
  timestamp: new Date().toISOString()
}));
```

## API Endpoints

### REST API

#### System Information
- `GET /api/linux-system?action=info` - Get system information
- `GET /api/linux-system?action=distribution` - Get distribution info
- `GET /api/linux-system?action=status` - Get API status

#### Command Execution
- `POST /api/linux-system/command` - Execute command
- `GET /api/linux-system/command?action=running` - Get running commands
- `DELETE /api/linux-system/command?id=<id>` - Kill running command

#### Package Management
- `GET /api/linux-system/packages?action=installed` - Get installed packages
- `GET /api/linux-system/packages?action=search&query=<term>` - Search packages
- `POST /api/linux-system/packages` - Install/remove packages
- `PUT /api/linux-system/packages` - Update/upgrade packages

#### WebSocket Information
- `GET /api/linux-system/ws` - Get WebSocket server info

### WebSocket Messages

#### Client to Server
```json
{
  "type": "command|subscribe|unsubscribe|monitor|ping",
  "id": "optional-request-id",
  "data": { /* request-specific data */ },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### Server to Client
```json
{
  "type": "result|error|event|pong",
  "id": "request-id",
  "data": { /* response data */ },
  "error": "error message if applicable",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Configuration

### System Manager Options

```typescript
const options = {
  distribution: 'auto', // 'ubuntu' | 'alpine' | 'auto'
  enableMonitoring: true,
  enableWebSocket: true,
  webSocketPort: 3002,
  enableSecurity: true,
  maxConcurrentCommands: 10,
  commandTimeout: 30000,
  monitoringConfig: {
    interval: 5000,
    metrics: ['cpu', 'memory', 'disk', 'network', 'processes', 'services'],
    thresholds: {
      cpu: 80,
      memory: 85,
      disk: 90
    }
  }
};
```

### Security Configuration

```typescript
const securityPolicy = {
  allowedCommands: ['ls', 'ps', 'top', 'htop'],
  blockedCommands: ['rm -rf /', 'dd if=/dev/zero'],
  requireSudo: false,
  maxCommandLength: 1000,
  allowFileAccess: true,
  allowedPaths: ['/home', '/tmp'],
  blockedPaths: ['/etc/passwd', '/etc/shadow'],
  enableAuditLog: true
};
```

## Components

### Linux System Dashboard

A comprehensive React component for system management:

```tsx
import { LinuxSystemDashboard } from '@/components/linux-system/linux-system-dashboard';

function App() {
  return <LinuxSystemDashboard />;
}
```

Features:
- Real-time system metrics
- Terminal interface
- Package management UI
- System information display
- WebSocket status indicator

## Supported Distributions

### Ubuntu
- Package Manager: `apt`
- Service Manager: `systemd`
- Init System: `systemd`

### Alpine Linux
- Package Manager: `apk`
- Service Manager: `openrc`
- Init System: `openrc`

## Security Considerations

1. **Command Validation**: All commands are validated against security policies
2. **Path Restrictions**: File operations are restricted to allowed paths
3. **User Permissions**: Commands can be restricted by user
4. **Audit Logging**: All operations are logged for security auditing
5. **Rate Limiting**: Prevents abuse through request limiting
6. **Sudo Requirements**: Privileged operations require explicit sudo

## Events

The system emits various events for monitoring:

- `system_metrics` - Real-time system performance data
- `package_events` - Package install/remove notifications
- `service_events` - Service status changes
- `process_events` - Process start/stop events
- `command_output` - Real-time command output
- `file_events` - File system operation events
- `network_events` - Network configuration changes

## Error Handling

All operations include comprehensive error handling:

```typescript
try {
  await systemManager.executeCommand('invalid-command');
} catch (error) {
  if (error.message.includes('Command not found')) {
    // Handle command not found
  } else if (error.message.includes('Permission denied')) {
    // Handle permission error
  }
}
```

## Performance

- **Monitoring Interval**: Configurable (default: 5 seconds)
- **WebSocket Connections**: Supports multiple concurrent connections
- **Command Timeout**: Configurable (default: 30 seconds)
- **Memory Usage**: Optimized for minimal memory footprint
- **CPU Impact**: Low-impact monitoring and command execution

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Check if port 3002 is available
   - Verify firewall settings
   - Ensure system manager is initialized

2. **Command Execution Failed**
   - Check security policy settings
   - Verify command exists on system
   - Check user permissions

3. **Package Operations Failed**
   - Ensure package manager is available
   - Check internet connectivity
   - Verify sudo permissions

### Debug Mode

Enable debug logging:

```typescript
const systemManager = SystemManager.getInstance({
  enableSecurity: false, // Disable for debugging
  // ... other options
});
```

## Contributing

The Linux System API is designed to be extensible. To add new features:

1. Create new service in `services/` directory
2. Add types to `types/index.ts`
3. Update `core/system-manager.ts` to integrate service
4. Add API routes in `app/api/linux-system/`
5. Update React hooks and components as needed

## License

This Linux System API is part of the larger application and follows the same licensing terms.
