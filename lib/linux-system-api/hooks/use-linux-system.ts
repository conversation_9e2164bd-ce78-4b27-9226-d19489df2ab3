/**
 * React Hook for Linux System API
 * 
 * Provides easy access to Linux system operations from React components
 */

import { useState, useEffect, useCallback } from 'react';
import { SystemInfo, PackageInfo, CommandResult, DistributionInfo } from '../types';

interface UseLinuxSystemOptions {
  autoInitialize?: boolean;
  enableWebSocket?: boolean;
}

interface UseLinuxSystemReturn {
  // State
  systemInfo: SystemInfo | null;
  distributionInfo: DistributionInfo | null;
  isLoading: boolean;
  error: string | null;
  isConnected: boolean;

  // System operations
  getSystemInfo: () => Promise<SystemInfo>;
  executeCommand: (command: string, options?: any) => Promise<CommandResult>;
  
  // Package management
  getInstalledPackages: () => Promise<PackageInfo[]>;
  searchPackages: (query: string) => Promise<PackageInfo[]>;
  installPackage: (packageName: string, options?: any) => Promise<void>;
  removePackage: (packageName: string, options?: any) => Promise<void>;
  updatePackageLists: () => Promise<void>;
  upgradePackages: (options?: any) => Promise<void>;

  // Utility functions
  refresh: () => Promise<void>;
  initialize: () => Promise<void>;
}

export function useLinuxSystem(options: UseLinuxSystemOptions = {}): UseLinuxSystemReturn {
  const { autoInitialize = true, enableWebSocket = false } = options;

  // State
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [distributionInfo, setDistributionInfo] = useState<DistributionInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  // API base URL
  const API_BASE = '/api/linux-system';

  // Helper function to make API calls
  const apiCall = useCallback(async (endpoint: string, options: RequestInit = {}) => {
    try {
      const response = await fetch(`${API_BASE}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    }
  }, []);

  // Initialize the system
  const initialize = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Initialize the system manager
      await apiCall('', {
        method: 'POST',
        body: JSON.stringify({ action: 'initialize' }),
      });

      // Get distribution info
      const distInfo = await apiCall('?action=distribution');
      setDistributionInfo(distInfo);

      // Get initial system info
      const sysInfo = await apiCall('?action=info');
      setSystemInfo(sysInfo);

      setIsConnected(true);
    } catch (err) {
      console.error('Failed to initialize Linux system:', err);
    } finally {
      setIsLoading(false);
    }
  }, [apiCall]);

  // Get system information
  const getSystemInfo = useCallback(async (): Promise<SystemInfo> => {
    const data = await apiCall('?action=info');
    setSystemInfo(data);
    return data;
  }, [apiCall]);

  // Execute command
  const executeCommand = useCallback(async (
    command: string, 
    options: any = {}
  ): Promise<CommandResult> => {
    const data = await apiCall('/command', {
      method: 'POST',
      body: JSON.stringify({ command, options }),
    });
    return data.result;
  }, [apiCall]);

  // Package management functions
  const getInstalledPackages = useCallback(async (): Promise<PackageInfo[]> => {
    const data = await apiCall('/packages?action=installed');
    return data.packages;
  }, [apiCall]);

  const searchPackages = useCallback(async (query: string): Promise<PackageInfo[]> => {
    const data = await apiCall(`/packages?action=search&query=${encodeURIComponent(query)}`);
    return data.packages;
  }, [apiCall]);

  const installPackage = useCallback(async (
    packageName: string, 
    options: any = {}
  ): Promise<void> => {
    await apiCall('/packages', {
      method: 'POST',
      body: JSON.stringify({ 
        action: 'install', 
        package: packageName, 
        options 
      }),
    });
  }, [apiCall]);

  const removePackage = useCallback(async (
    packageName: string, 
    options: any = {}
  ): Promise<void> => {
    await apiCall('/packages', {
      method: 'POST',
      body: JSON.stringify({ 
        action: 'remove', 
        package: packageName, 
        options 
      }),
    });
  }, [apiCall]);

  const updatePackageLists = useCallback(async (): Promise<void> => {
    await apiCall('/packages', {
      method: 'PUT',
      body: JSON.stringify({ action: 'update' }),
    });
  }, [apiCall]);

  const upgradePackages = useCallback(async (options: any = {}): Promise<void> => {
    await apiCall('/packages', {
      method: 'PUT',
      body: JSON.stringify({ action: 'upgrade', options }),
    });
  }, [apiCall]);

  // Refresh all data
  const refresh = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      await getSystemInfo();
    } catch (err) {
      console.error('Failed to refresh system data:', err);
    } finally {
      setIsLoading(false);
    }
  }, [getSystemInfo]);

  // Auto-initialize on mount
  useEffect(() => {
    if (autoInitialize) {
      initialize();
    }
  }, [autoInitialize, initialize]);

  // WebSocket connection (if enabled)
  useEffect(() => {
    if (!enableWebSocket || !isConnected) return;

    let ws: WebSocket | null = null;

    const connectWebSocket = () => {
      try {
        ws = new WebSocket('ws://localhost:3002/linux-system-ws');

        ws.onopen = () => {
          console.log('Linux System WebSocket connected');
        };

        ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            
            // Handle different message types
            switch (message.type) {
              case 'event':
                if (message.data?.type === 'system_metrics') {
                  // Update system info with new metrics
                  setSystemInfo(prev => prev ? { ...prev, ...message.data } : null);
                }
                break;
              
              case 'error':
                console.error('WebSocket error:', message.error);
                setError(message.error);
                break;
            }
          } catch (err) {
            console.error('Failed to parse WebSocket message:', err);
          }
        };

        ws.onclose = () => {
          console.log('Linux System WebSocket disconnected');
          // Attempt to reconnect after 5 seconds
          setTimeout(connectWebSocket, 5000);
        };

        ws.onerror = (error) => {
          console.error('WebSocket error:', error);
        };

        // Subscribe to system events
        ws.onopen = () => {
          ws?.send(JSON.stringify({
            type: 'subscribe',
            data: {
              events: ['system_metrics', 'package_events', 'service_events']
            },
            timestamp: new Date().toISOString()
          }));
        };

      } catch (err) {
        console.error('Failed to connect WebSocket:', err);
      }
    };

    connectWebSocket();

    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, [enableWebSocket, isConnected]);

  return {
    // State
    systemInfo,
    distributionInfo,
    isLoading,
    error,
    isConnected,

    // System operations
    getSystemInfo,
    executeCommand,

    // Package management
    getInstalledPackages,
    searchPackages,
    installPackage,
    removePackage,
    updatePackageLists,
    upgradePackages,

    // Utility functions
    refresh,
    initialize,
  };
}
