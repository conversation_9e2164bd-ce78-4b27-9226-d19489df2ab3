/**
 * WebSocket Server for Linux System API
 * 
 * Provides real-time communication for system operations and monitoring
 */

import { EventEmitter } from 'events';
import { WebSocketServer as WSServer, WebSocket } from 'ws';
import { IncomingMessage } from 'http';
import { 
  WebSocketMessage, 
  WebSocketResponse, 
  WebSocketConnection, 
  SystemCommand,
  CommandResult 
} from '../types';

export class WebSocketServer extends EventEmitter {
  private wss: WSServer | null = null;
  private connections = new Map<string, WebSocketConnection>();
  private port: number;
  private systemManager: any; // Will be properly typed when SystemManager is complete
  private pingInterval: NodeJS.Timeout | null = null;

  constructor(port: number, systemManager: any) {
    super();
    this.port = port;
    this.systemManager = systemManager;
  }

  /**
   * Start the WebSocket server
   */
  public async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.wss = new WSServer({ 
          port: this.port,
          path: '/linux-system-ws'
        });

        this.wss.on('connection', (ws: WebSocket, request: IncomingMessage) => {
          this.handleConnection(ws, request);
        });

        this.wss.on('listening', () => {
          console.log(`Linux System WebSocket server listening on port ${this.port}`);
          this.startPingInterval();
          resolve();
        });

        this.wss.on('error', (error) => {
          console.error('WebSocket server error:', error);
          reject(error);
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Handle new WebSocket connection
   */
  private handleConnection(ws: WebSocket, request: IncomingMessage): void {
    const connectionId = this.generateConnectionId();
    const connection: WebSocketConnection = {
      id: connectionId,
      socket: ws,
      subscriptions: new Set(),
      lastPing: Date.now(),
      connected: true
    };

    this.connections.set(connectionId, connection);
    this.emit('clientConnected', connection);

    // Send welcome message
    this.sendMessage(connection, {
      type: 'result',
      data: {
        message: 'Connected to Linux System API',
        connectionId,
        capabilities: [
          'command_execution',
          'system_monitoring',
          'file_operations',
          'service_management',
          'package_management',
          'process_management',
          'network_management'
        ]
      },
      timestamp: new Date().toISOString()
    });

    // Handle messages
    ws.on('message', (data) => {
      this.handleMessage(connection, data);
    });

    // Handle connection close
    ws.on('close', () => {
      this.handleDisconnection(connection);
    });

    // Handle errors
    ws.on('error', (error) => {
      console.error(`WebSocket error for connection ${connectionId}:`, error);
      this.handleDisconnection(connection);
    });

    // Handle pong responses
    ws.on('pong', () => {
      connection.lastPing = Date.now();
    });
  }

  /**
   * Handle incoming WebSocket message
   */
  private async handleMessage(connection: WebSocketConnection, data: any): Promise<void> {
    try {
      const message: WebSocketMessage = JSON.parse(data.toString());
      
      switch (message.type) {
        case 'ping':
          this.handlePing(connection, message);
          break;
          
        case 'command':
          await this.handleCommand(connection, message);
          break;
          
        case 'subscribe':
          this.handleSubscribe(connection, message);
          break;
          
        case 'unsubscribe':
          this.handleUnsubscribe(connection, message);
          break;
          
        case 'monitor':
          await this.handleMonitor(connection, message);
          break;
          
        default:
          this.sendError(connection, `Unknown message type: ${message.type}`, message.id);
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
      this.sendError(connection, 'Invalid message format');
    }
  }

  /**
   * Handle ping message
   */
  private handlePing(connection: WebSocketConnection, message: WebSocketMessage): void {
    connection.lastPing = Date.now();
    this.sendMessage(connection, {
      type: 'pong',
      id: message.id,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle command execution
   */
  private async handleCommand(connection: WebSocketConnection, message: WebSocketMessage): Promise<void> {
    try {
      const { command, args = [], options = {} } = message.data || {};
      
      if (!command) {
        this.sendError(connection, 'Command is required', message.id);
        return;
      }

      // Create system command
      const systemCommand: SystemCommand = {
        id: message.id || this.generateCommandId(),
        command,
        args,
        ...options
      };

      // Execute command with streaming output
      const commandExecutor = this.systemManager.commandExecutor;
      
      const result = await commandExecutor.executeStreaming(
        systemCommand,
        (data: string, type: 'stdout' | 'stderr') => {
          // Send streaming output
          this.sendMessage(connection, {
            type: 'event',
            id: message.id,
            data: {
              type: 'command_output',
              stream: type,
              data
            },
            timestamp: new Date().toISOString()
          });
        }
      );

      // Send final result
      this.sendMessage(connection, {
        type: 'result',
        id: message.id,
        data: result,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.sendError(connection, error instanceof Error ? error.message : String(error), message.id);
    }
  }

  /**
   * Handle subscription to events
   */
  private handleSubscribe(connection: WebSocketConnection, message: WebSocketMessage): void {
    const { events } = message.data || {};
    
    if (!events || !Array.isArray(events)) {
      this.sendError(connection, 'Events array is required', message.id);
      return;
    }

    for (const event of events) {
      connection.subscriptions.add(event);
    }

    this.sendMessage(connection, {
      type: 'result',
      id: message.id,
      data: {
        message: 'Subscribed to events',
        subscriptions: Array.from(connection.subscriptions)
      },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle unsubscription from events
   */
  private handleUnsubscribe(connection: WebSocketConnection, message: WebSocketMessage): void {
    const { events } = message.data || {};
    
    if (!events || !Array.isArray(events)) {
      this.sendError(connection, 'Events array is required', message.id);
      return;
    }

    for (const event of events) {
      connection.subscriptions.delete(event);
    }

    this.sendMessage(connection, {
      type: 'result',
      id: message.id,
      data: {
        message: 'Unsubscribed from events',
        subscriptions: Array.from(connection.subscriptions)
      },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle monitoring requests
   */
  private async handleMonitor(connection: WebSocketConnection, message: WebSocketMessage): Promise<void> {
    try {
      const { action, target } = message.data || {};
      
      switch (action) {
        case 'get_system_info':
          const systemInfo = await this.systemManager.getSystemInfo();
          this.sendMessage(connection, {
            type: 'result',
            id: message.id,
            data: systemInfo,
            timestamp: new Date().toISOString()
          });
          break;
          
        case 'get_processes':
          const processes = await this.systemManager.getProcessManager().getProcessList();
          this.sendMessage(connection, {
            type: 'result',
            id: message.id,
            data: processes,
            timestamp: new Date().toISOString()
          });
          break;
          
        case 'get_services':
          const services = await this.systemManager.getServiceManager().getServiceList();
          this.sendMessage(connection, {
            type: 'result',
            id: message.id,
            data: services,
            timestamp: new Date().toISOString()
          });
          break;
          
        default:
          this.sendError(connection, `Unknown monitor action: ${action}`, message.id);
      }
    } catch (error) {
      this.sendError(connection, error instanceof Error ? error.message : String(error), message.id);
    }
  }

  /**
   * Send message to connection
   */
  private sendMessage(connection: WebSocketConnection, message: WebSocketResponse): void {
    if (connection.connected && connection.socket.readyState === WebSocket.OPEN) {
      try {
        connection.socket.send(JSON.stringify(message));
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
        this.handleDisconnection(connection);
      }
    }
  }

  /**
   * Send error message
   */
  private sendError(connection: WebSocketConnection, error: string, id?: string): void {
    this.sendMessage(connection, {
      type: 'error',
      id,
      error,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Broadcast message to all subscribed connections
   */
  public broadcast(eventType: string, data: any): void {
    const message: WebSocketResponse = {
      type: 'event',
      data: {
        type: eventType,
        ...data
      },
      timestamp: new Date().toISOString()
    };

    for (const connection of this.connections.values()) {
      if (connection.subscriptions.has(eventType)) {
        this.sendMessage(connection, message);
      }
    }
  }

  /**
   * Handle connection disconnection
   */
  private handleDisconnection(connection: WebSocketConnection): void {
    connection.connected = false;
    this.connections.delete(connection.id);
    this.emit('clientDisconnected', connection);
  }

  /**
   * Start ping interval to keep connections alive
   */
  private startPingInterval(): void {
    this.pingInterval = setInterval(() => {
      const now = Date.now();
      
      for (const connection of this.connections.values()) {
        // Check if connection is stale (no pong received in 60 seconds)
        if (now - connection.lastPing > 60000) {
          console.log(`Closing stale connection: ${connection.id}`);
          connection.socket.terminate();
          this.handleDisconnection(connection);
          continue;
        }
        
        // Send ping
        if (connection.socket.readyState === WebSocket.OPEN) {
          connection.socket.ping();
        }
      }
    }, 30000); // Ping every 30 seconds
  }

  /**
   * Generate unique connection ID
   */
  private generateConnectionId(): string {
    return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique command ID
   */
  private generateCommandId(): string {
    return `cmd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get active connections count
   */
  public getConnectionsCount(): number {
    return this.connections.size;
  }

  /**
   * Get all connections
   */
  public getConnections(): WebSocketConnection[] {
    return Array.from(this.connections.values());
  }

  /**
   * Stop the WebSocket server
   */
  public async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.pingInterval) {
        clearInterval(this.pingInterval);
        this.pingInterval = null;
      }

      // Close all connections
      for (const connection of this.connections.values()) {
        connection.socket.close();
      }
      this.connections.clear();

      if (this.wss) {
        this.wss.close(() => {
          console.log('Linux System WebSocket server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}
