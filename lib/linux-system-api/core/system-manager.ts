/**
 * System Manager
 * 
 * Core manager for Linux system operations and monitoring
 */

import { EventEmitter } from 'events';
import { SystemInfo, SystemManagerOptions, DistributionInfo, SystemEvent, CommandResult } from '../types';
import { SystemDetector } from '../utils/system-detector';
import { CommandExecutor } from '../utils/command-executor';
import { SystemMonitor } from './system-monitor';
import { WebSocketServer } from './websocket-server';
import { PackageManager } from '../services/package-manager';
import { ServiceManager } from '../services/service-manager';
import { FilesystemManager } from '../services/filesystem-manager';
import { ProcessManager } from '../services/process-manager';
import { NetworkManager } from '../services/network-manager';

export class SystemManager extends EventEmitter {
  private static instance: SystemManager;
  private options: SystemManagerOptions;
  private distributionInfo: DistributionInfo | null = null;
  private systemDetector: SystemDetector;
  private commandExecutor: CommandExecutor;
  private systemMonitor: SystemMonitor | null = null;
  private webSocketServer: WebSocketServer | null = null;
  private packageManager: PackageManager | null = null;
  private serviceManager: ServiceManager | null = null;
  private filesystemManager: FilesystemManager | null = null;
  private processManager: ProcessManager | null = null;
  private networkManager: NetworkManager | null = null;
  private initialized = false;

  private constructor(options: SystemManagerOptions = {}) {
    super();
    this.options = {
      distribution: 'auto',
      enableMonitoring: true,
      enableWebSocket: true,
      webSocketPort: 3002,
      enableSecurity: true,
      maxConcurrentCommands: 10,
      commandTimeout: 30000,
      monitoringConfig: {
        interval: 5000,
        metrics: ['cpu', 'memory', 'disk', 'network', 'processes', 'services'],
        thresholds: {
          cpu: 80,
          memory: 85,
          disk: 90
        }
      },
      ...options
    };

    this.systemDetector = SystemDetector.getInstance();
    this.commandExecutor = CommandExecutor.getInstance(
      options.enableSecurity ? {
        allowedCommands: options.allowedCommands,
        blockedCommands: options.blockedCommands,
        enableAuditLog: true
      } : undefined,
      options.maxConcurrentCommands,
      options.commandTimeout
    );

    // Forward command events
    this.commandExecutor.on('commandStarted', (execution) => {
      this.emit('commandStarted', execution);
    });

    this.commandExecutor.on('commandCompleted', (execution) => {
      this.emit('commandCompleted', execution);
    });

    this.commandExecutor.on('commandFailed', (execution) => {
      this.emit('commandFailed', execution);
    });
  }

  public static getInstance(options?: SystemManagerOptions): SystemManager {
    if (!SystemManager.instance) {
      SystemManager.instance = new SystemManager(options);
    }
    return SystemManager.instance;
  }

  /**
   * Initialize the system manager
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Detect distribution
      this.distributionInfo = await this.systemDetector.detectDistribution();
      this.emit('distributionDetected', this.distributionInfo);

      // Initialize services
      await this.initializeServices();

      // Start monitoring if enabled
      if (this.options.enableMonitoring) {
        await this.startMonitoring();
      }

      // Start WebSocket server if enabled
      if (this.options.enableWebSocket) {
        await this.startWebSocketServer();
      }

      this.initialized = true;
      this.emit('initialized', this.distributionInfo);
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Initialize system services
   */
  private async initializeServices(): Promise<void> {
    if (!this.distributionInfo) {
      throw new Error('Distribution not detected');
    }

    // Initialize package manager
    this.packageManager = new PackageManager(
      this.distributionInfo.packageManager,
      this.commandExecutor
    );

    // Initialize service manager
    this.serviceManager = new ServiceManager(
      this.distributionInfo.serviceManager,
      this.commandExecutor
    );

    // Initialize filesystem manager
    this.filesystemManager = new FilesystemManager(this.commandExecutor);

    // Initialize process manager
    this.processManager = new ProcessManager(this.commandExecutor);

    // Initialize network manager
    this.networkManager = new NetworkManager(this.commandExecutor);

    // Forward service events
    this.packageManager.on('packageInstalled', (pkg) => {
      this.emit('systemEvent', {
        type: 'package_install',
        data: pkg,
        timestamp: new Date().toISOString(),
        source: 'package-manager'
      } as SystemEvent);
    });

    this.serviceManager.on('serviceStarted', (service) => {
      this.emit('systemEvent', {
        type: 'service_change',
        data: { service, action: 'started' },
        timestamp: new Date().toISOString(),
        source: 'service-manager'
      } as SystemEvent);
    });
  }

  /**
   * Start system monitoring
   */
  private async startMonitoring(): Promise<void> {
    if (!this.options.monitoringConfig) {
      return;
    }

    this.systemMonitor = new SystemMonitor(
      this.options.monitoringConfig,
      this.commandExecutor
    );

    // Forward monitoring events
    this.systemMonitor.on('metricsUpdated', (metrics) => {
      this.emit('metricsUpdated', metrics);
    });

    this.systemMonitor.on('thresholdExceeded', (alert) => {
      this.emit('thresholdExceeded', alert);
    });

    await this.systemMonitor.start();
  }

  /**
   * Start WebSocket server
   */
  private async startWebSocketServer(): Promise<void> {
    this.webSocketServer = new WebSocketServer(
      this.options.webSocketPort || 3002,
      this
    );

    // Forward WebSocket events
    this.webSocketServer.on('clientConnected', (client) => {
      this.emit('clientConnected', client);
    });

    this.webSocketServer.on('clientDisconnected', (client) => {
      this.emit('clientDisconnected', client);
    });

    await this.webSocketServer.start();
  }

  /**
   * Get comprehensive system information
   */
  public async getSystemInfo(): Promise<SystemInfo> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.distributionInfo) {
      throw new Error('System not initialized');
    }

    const [
      hostname,
      architecture,
      kernel,
      uptime,
      loadAverage,
      memory,
      disk,
      network,
      processes,
      services
    ] = await Promise.all([
      this.systemDetector.getHostname(),
      this.systemDetector.getArchitecture(),
      this.systemDetector.getKernelVersion(),
      this.systemDetector.getUptime(),
      this.systemDetector.getLoadAverage(),
      this.systemMonitor?.getMemoryInfo() || this.getMemoryInfoFallback(),
      this.systemMonitor?.getDiskInfo() || [],
      this.systemMonitor?.getNetworkInfo() || [],
      this.processManager?.getProcessList() || [],
      this.serviceManager?.getServiceList() || []
    ]);

    return {
      id: `${hostname}-${Date.now()}`,
      hostname,
      distribution: this.distributionInfo.name,
      version: this.distributionInfo.version,
      architecture,
      kernel,
      uptime,
      loadAverage,
      memory,
      disk,
      network,
      processes,
      services
    };
  }

  /**
   * Execute a command
   */
  public async executeCommand(command: string, options: {
    timeout?: number;
    cwd?: string;
    env?: Record<string, string>;
    sudo?: boolean;
  } = {}): Promise<CommandResult> {
    if (!this.initialized) {
      await this.initialize();
    }

    return this.commandExecutor.executeSimple(command, options);
  }

  /**
   * Get package manager instance
   */
  public getPackageManager(): PackageManager {
    if (!this.packageManager) {
      throw new Error('Package manager not initialized');
    }
    return this.packageManager;
  }

  /**
   * Get service manager instance
   */
  public getServiceManager(): ServiceManager {
    if (!this.serviceManager) {
      throw new Error('Service manager not initialized');
    }
    return this.serviceManager;
  }

  /**
   * Get filesystem manager instance
   */
  public getFilesystemManager(): FilesystemManager {
    if (!this.filesystemManager) {
      throw new Error('Filesystem manager not initialized');
    }
    return this.filesystemManager;
  }

  /**
   * Get process manager instance
   */
  public getProcessManager(): ProcessManager {
    if (!this.processManager) {
      throw new Error('Process manager not initialized');
    }
    return this.processManager;
  }

  /**
   * Get network manager instance
   */
  public getNetworkManager(): NetworkManager {
    if (!this.networkManager) {
      throw new Error('Network manager not initialized');
    }
    return this.networkManager;
  }

  /**
   * Get WebSocket server instance
   */
  public getWebSocketServer(): WebSocketServer | null {
    return this.webSocketServer;
  }

  /**
   * Get distribution information
   */
  public getDistributionInfo(): DistributionInfo | null {
    return this.distributionInfo;
  }

  /**
   * Fallback memory info getter
   */
  private async getMemoryInfoFallback() {
    try {
      const result = await this.commandExecutor.executeSimple('cat /proc/meminfo');
      const lines = result.stdout.split('\n');
      const memInfo: any = {};

      for (const line of lines) {
        const [key, value] = line.split(':');
        if (key && value) {
          const numValue = parseInt(value.trim().split(' ')[0]) * 1024; // Convert KB to bytes
          memInfo[key.trim()] = numValue;
        }
      }

      return {
        total: memInfo.MemTotal || 0,
        free: memInfo.MemFree || 0,
        available: memInfo.MemAvailable || 0,
        used: (memInfo.MemTotal || 0) - (memInfo.MemFree || 0),
        cached: memInfo.Cached || 0,
        buffers: memInfo.Buffers || 0,
        swapTotal: memInfo.SwapTotal || 0,
        swapFree: memInfo.SwapFree || 0,
        swapUsed: (memInfo.SwapTotal || 0) - (memInfo.SwapFree || 0)
      };
    } catch (error) {
      return {
        total: 0,
        free: 0,
        available: 0,
        used: 0,
        cached: 0,
        buffers: 0,
        swapTotal: 0,
        swapFree: 0,
        swapUsed: 0
      };
    }
  }

  /**
   * Shutdown the system manager
   */
  public async shutdown(): Promise<void> {
    if (this.systemMonitor) {
      await this.systemMonitor.stop();
    }

    if (this.webSocketServer) {
      await this.webSocketServer.stop();
    }

    this.initialized = false;
    this.emit('shutdown');
  }
}
