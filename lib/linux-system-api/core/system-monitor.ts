/**
 * System Monitor
 * 
 * Monitors system metrics and provides real-time updates
 */

import { EventEmitter } from 'events';
import { 
  MonitoringConfig, 
  SystemMetrics, 
  MemoryInfo, 
  DiskInfo, 
  NetworkInterface,
  CommandExecutor 
} from '../types';

export class SystemMonitor extends EventEmitter {
  private config: MonitoringConfig;
  private commandExecutor: CommandExecutor;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  private lastMetrics: SystemMetrics | null = null;
  private lastNetworkStats: Map<string, { rxBytes: number; txBytes: number }> = new Map();

  constructor(config: MonitoringConfig, commandExecutor: CommandExecutor) {
    super();
    this.config = config;
    this.commandExecutor = commandExecutor;
  }

  /**
   * Start monitoring
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.emit('started');

    // Initial metrics collection
    await this.collectMetrics();

    // Start periodic monitoring
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.collectMetrics();
      } catch (error) {
        console.error('Error collecting metrics:', error);
        this.emit('error', error);
      }
    }, this.config.interval);
  }

  /**
   * Stop monitoring
   */
  public async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    this.emit('stopped');
  }

  /**
   * Collect all system metrics
   */
  private async collectMetrics(): Promise<void> {
    const timestamp = new Date().toISOString();
    const metrics: SystemMetrics = {
      timestamp,
      cpu: await this.getCpuMetrics(),
      memory: await this.getMemoryInfo(),
      disk: await this.getDiskInfo(),
      network: await this.getNetworkMetrics(),
      processes: await this.getProcessMetrics(),
      services: await this.getServiceMetrics()
    };

    this.lastMetrics = metrics;
    this.emit('metricsUpdated', metrics);

    // Check thresholds
    this.checkThresholds(metrics);
  }

  /**
   * Get CPU metrics
   */
  private async getCpuMetrics(): Promise<{ usage: number; loadAverage: number[]; cores: number }> {
    try {
      // Get CPU usage from /proc/stat
      const statResult = await this.commandExecutor.executeSimple('cat /proc/stat');
      const cpuLine = statResult.stdout.split('\n')[0];
      const cpuValues = cpuLine.split(/\s+/).slice(1).map(Number);
      
      const idle = cpuValues[3];
      const total = cpuValues.reduce((sum, val) => sum + val, 0);
      const usage = Math.round(((total - idle) / total) * 100);

      // Get load average
      const loadResult = await this.commandExecutor.executeSimple('cat /proc/loadavg');
      const loadValues = loadResult.stdout.trim().split(' ').slice(0, 3).map(Number);

      // Get CPU cores
      const coresResult = await this.commandExecutor.executeSimple('nproc');
      const cores = parseInt(coresResult.stdout.trim()) || 1;

      return {
        usage: isNaN(usage) ? 0 : usage,
        loadAverage: loadValues,
        cores
      };
    } catch (error) {
      console.error('Error getting CPU metrics:', error);
      return { usage: 0, loadAverage: [0, 0, 0], cores: 1 };
    }
  }

  /**
   * Get memory information
   */
  public async getMemoryInfo(): Promise<MemoryInfo> {
    try {
      const result = await this.commandExecutor.executeSimple('cat /proc/meminfo');
      const lines = result.stdout.split('\n');
      const memInfo: any = {};

      for (const line of lines) {
        const [key, value] = line.split(':');
        if (key && value) {
          const numValue = parseInt(value.trim().split(' ')[0]) * 1024; // Convert KB to bytes
          memInfo[key.trim()] = numValue;
        }
      }

      return {
        total: memInfo.MemTotal || 0,
        free: memInfo.MemFree || 0,
        available: memInfo.MemAvailable || memInfo.MemFree || 0,
        used: (memInfo.MemTotal || 0) - (memInfo.MemFree || 0),
        cached: memInfo.Cached || 0,
        buffers: memInfo.Buffers || 0,
        swapTotal: memInfo.SwapTotal || 0,
        swapFree: memInfo.SwapFree || 0,
        swapUsed: (memInfo.SwapTotal || 0) - (memInfo.SwapFree || 0)
      };
    } catch (error) {
      console.error('Error getting memory info:', error);
      return {
        total: 0,
        free: 0,
        available: 0,
        used: 0,
        cached: 0,
        buffers: 0,
        swapTotal: 0,
        swapFree: 0,
        swapUsed: 0
      };
    }
  }

  /**
   * Get disk information
   */
  public async getDiskInfo(): Promise<DiskInfo[]> {
    try {
      const result = await this.commandExecutor.executeSimple('df -B1');
      const lines = result.stdout.split('\n').slice(1); // Skip header
      const diskInfo: DiskInfo[] = [];

      for (const line of lines) {
        if (!line.trim()) continue;
        
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 6) {
          const [device, size, used, available, , mountpoint] = parts;
          
          // Skip special filesystems
          if (device.startsWith('/dev/') || mountpoint === '/') {
            diskInfo.push({
              device,
              mountpoint,
              filesystem: 'unknown', // df doesn't provide filesystem type
              size: parseInt(size) || 0,
              used: parseInt(used) || 0,
              available: parseInt(available) || 0,
              usePercent: Math.round(((parseInt(used) || 0) / (parseInt(size) || 1)) * 100)
            });
          }
        }
      }

      return diskInfo;
    } catch (error) {
      console.error('Error getting disk info:', error);
      return [];
    }
  }

  /**
   * Get network metrics
   */
  private async getNetworkMetrics(): Promise<{ interfaces: NetworkInterface[]; totalRx: number; totalTx: number }> {
    try {
      const interfaces = await this.getNetworkInfo();
      const totalRx = interfaces.reduce((sum, iface) => sum + iface.rxBytes, 0);
      const totalTx = interfaces.reduce((sum, iface) => sum + iface.txBytes, 0);

      return { interfaces, totalRx, totalTx };
    } catch (error) {
      console.error('Error getting network metrics:', error);
      return { interfaces: [], totalRx: 0, totalTx: 0 };
    }
  }

  /**
   * Get network interface information
   */
  public async getNetworkInfo(): Promise<NetworkInterface[]> {
    try {
      const result = await this.commandExecutor.executeSimple('cat /proc/net/dev');
      const lines = result.stdout.split('\n').slice(2); // Skip headers
      const interfaces: NetworkInterface[] = [];

      for (const line of lines) {
        if (!line.trim()) continue;
        
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 17) {
          const name = parts[0].replace(':', '');
          const rxBytes = parseInt(parts[1]) || 0;
          const rxPackets = parseInt(parts[2]) || 0;
          const txBytes = parseInt(parts[9]) || 0;
          const txPackets = parseInt(parts[10]) || 0;

          // Skip loopback interface
          if (name !== 'lo') {
            interfaces.push({
              name,
              type: name.startsWith('eth') ? 'ethernet' : name.startsWith('wlan') ? 'wireless' : 'unknown',
              state: 'up', // We'll assume up if it has stats
              mac: 'unknown', // Would need additional command to get MAC
              rxBytes,
              txBytes,
              rxPackets,
              txPackets
            });
          }
        }
      }

      return interfaces;
    } catch (error) {
      console.error('Error getting network info:', error);
      return [];
    }
  }

  /**
   * Get process metrics
   */
  private async getProcessMetrics(): Promise<{ total: number; running: number; sleeping: number; zombie: number }> {
    try {
      const result = await this.commandExecutor.executeSimple('ps axo stat --no-headers');
      const states = result.stdout.split('\n').filter(line => line.trim());
      
      let running = 0;
      let sleeping = 0;
      let zombie = 0;

      for (const state of states) {
        const firstChar = state.trim().charAt(0);
        switch (firstChar) {
          case 'R':
            running++;
            break;
          case 'S':
          case 'D':
            sleeping++;
            break;
          case 'Z':
            zombie++;
            break;
        }
      }

      return {
        total: states.length,
        running,
        sleeping,
        zombie
      };
    } catch (error) {
      console.error('Error getting process metrics:', error);
      return { total: 0, running: 0, sleeping: 0, zombie: 0 };
    }
  }

  /**
   * Get service metrics
   */
  private async getServiceMetrics(): Promise<{ total: number; active: number; failed: number }> {
    try {
      // Try systemctl first (systemd)
      try {
        const result = await this.commandExecutor.executeSimple('systemctl list-units --type=service --no-pager --no-legend');
        const services = result.stdout.split('\n').filter(line => line.trim());
        
        let active = 0;
        let failed = 0;

        for (const service of services) {
          if (service.includes('active')) active++;
          if (service.includes('failed')) failed++;
        }

        return {
          total: services.length,
          active,
          failed
        };
      } catch (systemdError) {
        // Fallback for OpenRC (Alpine)
        const result = await this.commandExecutor.executeSimple('rc-status --servicelist');
        const services = result.stdout.split('\n').filter(line => line.trim());
        
        return {
          total: services.length,
          active: services.length, // Assume all listed services are active
          failed: 0
        };
      }
    } catch (error) {
      console.error('Error getting service metrics:', error);
      return { total: 0, active: 0, failed: 0 };
    }
  }

  /**
   * Check thresholds and emit alerts
   */
  private checkThresholds(metrics: SystemMetrics): void {
    const thresholds = this.config.thresholds;
    
    if (thresholds?.cpu && metrics.cpu.usage > thresholds.cpu) {
      this.emit('thresholdExceeded', {
        type: 'cpu',
        value: metrics.cpu.usage,
        threshold: thresholds.cpu,
        timestamp: metrics.timestamp
      });
    }

    if (thresholds?.memory) {
      const memoryUsagePercent = (metrics.memory.used / metrics.memory.total) * 100;
      if (memoryUsagePercent > thresholds.memory) {
        this.emit('thresholdExceeded', {
          type: 'memory',
          value: memoryUsagePercent,
          threshold: thresholds.memory,
          timestamp: metrics.timestamp
        });
      }
    }

    if (thresholds?.disk) {
      for (const disk of metrics.disk) {
        if (disk.usePercent > thresholds.disk) {
          this.emit('thresholdExceeded', {
            type: 'disk',
            value: disk.usePercent,
            threshold: thresholds.disk,
            device: disk.device,
            mountpoint: disk.mountpoint,
            timestamp: metrics.timestamp
          });
        }
      }
    }
  }

  /**
   * Get last collected metrics
   */
  public getLastMetrics(): SystemMetrics | null {
    return this.lastMetrics;
  }

  /**
   * Update monitoring configuration
   */
  public updateConfig(config: Partial<MonitoringConfig>): void {
    this.config = { ...this.config, ...config };
    
    // Restart monitoring with new interval if changed
    if (config.interval && this.isRunning) {
      this.stop().then(() => this.start());
    }
  }
}
