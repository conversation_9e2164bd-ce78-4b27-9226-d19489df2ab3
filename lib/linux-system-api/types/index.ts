/**
 * Linux System API Types
 */

export interface SystemInfo {
  id: string;
  hostname: string;
  distribution: 'ubuntu' | 'alpine';
  version: string;
  architecture: string;
  kernel: string;
  uptime: number;
  loadAverage: number[];
  memory: MemoryInfo;
  disk: DiskInfo[];
  network: NetworkInterface[];
  processes: ProcessInfo[];
  services: ServiceInfo[];
}

export interface MemoryInfo {
  total: number;
  free: number;
  available: number;
  used: number;
  cached: number;
  buffers: number;
  swapTotal: number;
  swapFree: number;
  swapUsed: number;
}

export interface DiskInfo {
  device: string;
  mountpoint: string;
  filesystem: string;
  size: number;
  used: number;
  available: number;
  usePercent: number;
}

export interface NetworkInterface {
  name: string;
  type: string;
  state: 'up' | 'down';
  ipv4?: string;
  ipv6?: string;
  mac: string;
  rxBytes: number;
  txBytes: number;
  rxPackets: number;
  txPackets: number;
}

export interface ProcessInfo {
  pid: number;
  ppid: number;
  name: string;
  command: string;
  user: string;
  state: string;
  cpu: number;
  memory: number;
  startTime: string;
}

export interface ServiceInfo {
  name: string;
  status: 'active' | 'inactive' | 'failed' | 'unknown';
  enabled: boolean;
  description: string;
  mainPid?: number;
  memory?: number;
  cpu?: number;
}

export interface PackageInfo {
  name: string;
  version: string;
  description: string;
  size: number;
  installed: boolean;
  upgradable: boolean;
  newVersion?: string;
}

export interface CommandResult {
  stdout: string;
  stderr: string;
  exitCode: number;
  duration: number;
  timestamp: string;
}

export interface WebSocketMessage {
  type: 'command' | 'monitor' | 'subscribe' | 'unsubscribe' | 'ping' | 'pong';
  id?: string;
  data?: any;
  timestamp: string;
}

export interface WebSocketResponse {
  type: 'result' | 'error' | 'event' | 'pong';
  id?: string;
  data?: any;
  error?: string;
  timestamp: string;
}

export interface SystemEvent {
  type: 'process_start' | 'process_stop' | 'service_change' | 'package_install' | 'package_remove' | 'file_change' | 'network_change' | 'system_update';
  data: any;
  timestamp: string;
  source: string;
}

export interface MonitoringConfig {
  interval: number;
  metrics: ('cpu' | 'memory' | 'disk' | 'network' | 'processes' | 'services')[];
  thresholds: {
    cpu?: number;
    memory?: number;
    disk?: number;
  };
}

export interface SystemManagerOptions {
  distribution?: 'ubuntu' | 'alpine' | 'auto';
  enableMonitoring?: boolean;
  monitoringConfig?: MonitoringConfig;
  enableWebSocket?: boolean;
  webSocketPort?: number;
  enableSecurity?: boolean;
  allowedCommands?: string[];
  blockedCommands?: string[];
  maxConcurrentCommands?: number;
  commandTimeout?: number;
}

export interface FileSystemOperation {
  type: 'read' | 'write' | 'delete' | 'copy' | 'move' | 'mkdir' | 'rmdir' | 'chmod' | 'chown';
  path: string;
  data?: string | Buffer;
  destination?: string;
  mode?: string;
  owner?: string;
  group?: string;
  recursive?: boolean;
}

export interface NetworkConfiguration {
  interface: string;
  type: 'static' | 'dhcp';
  ipv4?: string;
  netmask?: string;
  gateway?: string;
  dns?: string[];
  ipv6?: string;
  ipv6Gateway?: string;
}

export interface SecurityPolicy {
  allowedUsers?: string[];
  allowedCommands?: string[];
  blockedCommands?: string[];
  requireSudo?: boolean;
  maxCommandLength?: number;
  allowFileAccess?: boolean;
  allowedPaths?: string[];
  blockedPaths?: string[];
  enableAuditLog?: boolean;
}

export interface AuditLogEntry {
  id: string;
  timestamp: string;
  user: string;
  command: string;
  result: 'success' | 'failure';
  exitCode?: number;
  duration: number;
  source: 'api' | 'websocket' | 'direct';
}

export interface SystemMetrics {
  timestamp: string;
  cpu: {
    usage: number;
    loadAverage: number[];
    cores: number;
  };
  memory: MemoryInfo;
  disk: DiskInfo[];
  network: {
    interfaces: NetworkInterface[];
    totalRx: number;
    totalTx: number;
  };
  processes: {
    total: number;
    running: number;
    sleeping: number;
    zombie: number;
  };
  services: {
    total: number;
    active: number;
    failed: number;
  };
}

export interface WebSocketConnection {
  id: string;
  socket: WebSocket;
  user?: string;
  subscriptions: Set<string>;
  lastPing: number;
  connected: boolean;
}

export interface SystemCommand {
  id: string;
  command: string;
  args: string[];
  user?: string;
  cwd?: string;
  env?: Record<string, string>;
  timeout?: number;
  sudo?: boolean;
  background?: boolean;
}

export interface CommandExecution {
  id: string;
  command: SystemCommand;
  startTime: string;
  endTime?: string;
  result?: CommandResult;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'timeout';
  pid?: number;
}

export type SystemDistribution = 'ubuntu' | 'alpine';
export type ServiceManager = 'systemd' | 'openrc';
export type PackageManager = 'apt' | 'apk';

export interface DistributionInfo {
  name: SystemDistribution;
  version: string;
  codename?: string;
  serviceManager: ServiceManager;
  packageManager: PackageManager;
  initSystem: string;
  shell: string;
}
