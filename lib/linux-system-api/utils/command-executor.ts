/**
 * Command Executor Utility
 * 
 * Secure command execution with timeout, validation, and monitoring
 */

import { exec, spawn, ChildProcess } from 'child_process';
import { promisify } from 'util';
import { EventEmitter } from 'events';
import { SystemCommand, CommandResult, CommandExecution, SecurityPolicy } from '../types';
import { SecurityValidator } from './security-validator';

const execAsync = promisify(exec);

export class CommandExecutor extends EventEmitter {
  private static instance: CommandExecutor;
  private runningCommands = new Map<string, CommandExecution>();
  private securityValidator: SecurityValidator;
  private maxConcurrentCommands: number;
  private defaultTimeout: number;

  private constructor(
    securityPolicy?: SecurityPolicy,
    maxConcurrentCommands = 10,
    defaultTimeout = 30000
  ) {
    super();
    this.securityValidator = new SecurityValidator(securityPolicy);
    this.maxConcurrentCommands = maxConcurrentCommands;
    this.defaultTimeout = defaultTimeout;
  }

  public static getInstance(
    securityPolicy?: SecurityPolicy,
    maxConcurrentCommands = 10,
    defaultTimeout = 30000
  ): CommandExecutor {
    if (!CommandExecutor.instance) {
      CommandExecutor.instance = new CommandExecutor(
        securityPolicy,
        maxConcurrentCommands,
        defaultTimeout
      );
    }
    return CommandExecutor.instance;
  }

  /**
   * Execute a command with full monitoring and security
   */
  public async executeCommand(command: SystemCommand): Promise<CommandResult> {
    // Validate command
    await this.securityValidator.validateCommand(command);

    // Check concurrent command limit
    if (this.runningCommands.size >= this.maxConcurrentCommands) {
      throw new Error('Maximum concurrent commands reached');
    }

    const execution: CommandExecution = {
      id: command.id,
      command,
      startTime: new Date().toISOString(),
      status: 'pending'
    };

    this.runningCommands.set(command.id, execution);
    this.emit('commandStarted', execution);

    try {
      execution.status = 'running';
      this.emit('commandRunning', execution);

      const result = await this.executeInternal(command);
      
      execution.endTime = new Date().toISOString();
      execution.result = result;
      execution.status = 'completed';
      
      this.emit('commandCompleted', execution);
      return result;
    } catch (error) {
      execution.endTime = new Date().toISOString();
      execution.status = 'failed';
      execution.result = {
        stdout: '',
        stderr: error instanceof Error ? error.message : String(error),
        exitCode: 1,
        duration: Date.now() - new Date(execution.startTime).getTime(),
        timestamp: new Date().toISOString()
      };
      
      this.emit('commandFailed', execution);
      throw error;
    } finally {
      this.runningCommands.delete(command.id);
    }
  }

  /**
   * Execute a simple command string
   */
  public async executeSimple(
    commandString: string,
    options: {
      timeout?: number;
      cwd?: string;
      env?: Record<string, string>;
      user?: string;
      sudo?: boolean;
    } = {}
  ): Promise<CommandResult> {
    const command: SystemCommand = {
      id: `simple_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      command: commandString.split(' ')[0],
      args: commandString.split(' ').slice(1),
      timeout: options.timeout || this.defaultTimeout,
      cwd: options.cwd,
      env: options.env,
      user: options.user,
      sudo: options.sudo
    };

    return this.executeCommand(command);
  }

  /**
   * Internal command execution
   */
  private async executeInternal(command: SystemCommand): Promise<CommandResult> {
    const startTime = Date.now();
    const timeout = command.timeout || this.defaultTimeout;

    // Build the full command string
    let fullCommand = this.buildCommandString(command);

    // Add sudo if required
    if (command.sudo) {
      fullCommand = `sudo ${fullCommand}`;
    }

    try {
      const { stdout, stderr } = await execAsync(fullCommand, {
        timeout,
        cwd: command.cwd,
        env: { ...process.env, ...command.env },
        maxBuffer: 1024 * 1024 * 10 // 10MB buffer
      });

      const duration = Date.now() - startTime;

      return {
        stdout: stdout || '',
        stderr: stderr || '',
        exitCode: 0,
        duration,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      const duration = Date.now() - startTime;

      return {
        stdout: error.stdout || '',
        stderr: error.stderr || error.message || '',
        exitCode: error.code || 1,
        duration,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Execute a command with streaming output
   */
  public executeStreaming(
    command: SystemCommand,
    onData: (data: string, type: 'stdout' | 'stderr') => void
  ): Promise<CommandResult> {
    return new Promise((resolve, reject) => {
      // Validate command
      this.securityValidator.validateCommand(command).catch(reject);

      const execution: CommandExecution = {
        id: command.id,
        command,
        startTime: new Date().toISOString(),
        status: 'running'
      };

      this.runningCommands.set(command.id, execution);
      this.emit('commandStarted', execution);

      const startTime = Date.now();
      let stdout = '';
      let stderr = '';

      // Build command
      let fullCommand = this.buildCommandString(command);
      if (command.sudo) {
        fullCommand = `sudo ${fullCommand}`;
      }

      // Spawn process
      const child = spawn('sh', ['-c', fullCommand], {
        cwd: command.cwd,
        env: { ...process.env, ...command.env },
        stdio: ['pipe', 'pipe', 'pipe']
      });

      execution.pid = child.pid;

      // Handle stdout
      child.stdout?.on('data', (data) => {
        const chunk = data.toString();
        stdout += chunk;
        onData(chunk, 'stdout');
      });

      // Handle stderr
      child.stderr?.on('data', (data) => {
        const chunk = data.toString();
        stderr += chunk;
        onData(chunk, 'stderr');
      });

      // Handle process completion
      child.on('close', (code) => {
        const duration = Date.now() - startTime;
        const result: CommandResult = {
          stdout,
          stderr,
          exitCode: code || 0,
          duration,
          timestamp: new Date().toISOString()
        };

        execution.endTime = new Date().toISOString();
        execution.result = result;
        execution.status = code === 0 ? 'completed' : 'failed';

        this.runningCommands.delete(command.id);
        this.emit(code === 0 ? 'commandCompleted' : 'commandFailed', execution);

        if (code === 0) {
          resolve(result);
        } else {
          reject(new Error(`Command failed with exit code ${code}`));
        }
      });

      // Handle errors
      child.on('error', (error) => {
        execution.endTime = new Date().toISOString();
        execution.status = 'failed';
        this.runningCommands.delete(command.id);
        this.emit('commandFailed', execution);
        reject(error);
      });

      // Set timeout
      const timeoutId = setTimeout(() => {
        child.kill('SIGTERM');
        setTimeout(() => {
          if (!child.killed) {
            child.kill('SIGKILL');
          }
        }, 5000);
        
        execution.status = 'timeout';
        this.runningCommands.delete(command.id);
        reject(new Error('Command timeout'));
      }, command.timeout || this.defaultTimeout);

      child.on('close', () => {
        clearTimeout(timeoutId);
      });
    });
  }

  /**
   * Build command string from SystemCommand
   */
  private buildCommandString(command: SystemCommand): string {
    const args = command.args.map(arg => {
      // Escape arguments that contain spaces or special characters
      if (arg.includes(' ') || arg.includes('"') || arg.includes("'")) {
        return `"${arg.replace(/"/g, '\\"')}"`;
      }
      return arg;
    });

    return `${command.command} ${args.join(' ')}`.trim();
  }

  /**
   * Kill a running command
   */
  public async killCommand(commandId: string): Promise<boolean> {
    const execution = this.runningCommands.get(commandId);
    if (!execution || !execution.pid) {
      return false;
    }

    try {
      process.kill(execution.pid, 'SIGTERM');
      
      // Force kill after 5 seconds if still running
      setTimeout(() => {
        if (this.runningCommands.has(commandId)) {
          try {
            process.kill(execution.pid!, 'SIGKILL');
          } catch (error) {
            // Process might already be dead
          }
        }
      }, 5000);

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get running commands
   */
  public getRunningCommands(): CommandExecution[] {
    return Array.from(this.runningCommands.values());
  }

  /**
   * Get command by ID
   */
  public getCommand(commandId: string): CommandExecution | undefined {
    return this.runningCommands.get(commandId);
  }

  /**
   * Update security policy
   */
  public updateSecurityPolicy(policy: SecurityPolicy): void {
    this.securityValidator.updatePolicy(policy);
  }
}
