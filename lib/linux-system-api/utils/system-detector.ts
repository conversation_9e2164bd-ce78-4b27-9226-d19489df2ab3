/**
 * System Detector Utility
 * 
 * Detects the Linux distribution and system characteristics
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import { readFile, access } from 'fs/promises';
import { DistributionInfo, SystemDistribution, ServiceManager, PackageManager } from '../types';

const execAsync = promisify(exec);

export class SystemDetector {
  private static instance: SystemDetector;
  private distributionInfo: DistributionInfo | null = null;

  private constructor() {}

  public static getInstance(): SystemDetector {
    if (!SystemDetector.instance) {
      SystemDetector.instance = new SystemDetector();
    }
    return SystemDetector.instance;
  }

  /**
   * Detect the Linux distribution and system information
   */
  public async detectDistribution(): Promise<DistributionInfo> {
    if (this.distributionInfo) {
      return this.distributionInfo;
    }

    try {
      // Try to read /etc/os-release first (most modern distributions)
      const osRelease = await this.readOsRelease();
      if (osRelease) {
        this.distributionInfo = osRelease;
        return osRelease;
      }

      // Fallback to other methods
      const fallbackInfo = await this.detectFallback();
      this.distributionInfo = fallbackInfo;
      return fallbackInfo;
    } catch (error) {
      console.error('Failed to detect distribution:', error);
      throw new Error('Unable to detect Linux distribution');
    }
  }

  /**
   * Read and parse /etc/os-release
   */
  private async readOsRelease(): Promise<DistributionInfo | null> {
    try {
      await access('/etc/os-release');
      const content = await readFile('/etc/os-release', 'utf-8');
      const lines = content.split('\n');
      const info: Record<string, string> = {};

      for (const line of lines) {
        const [key, value] = line.split('=');
        if (key && value) {
          info[key] = value.replace(/"/g, '');
        }
      }

      const name = this.normalizeDistributionName(info.ID || info.NAME || '');
      const version = info.VERSION_ID || info.VERSION || 'unknown';
      const codename = info.VERSION_CODENAME || info.UBUNTU_CODENAME;

      if (!name) {
        return null;
      }

      return {
        name,
        version,
        codename,
        serviceManager: await this.detectServiceManager(),
        packageManager: this.getPackageManager(name),
        initSystem: await this.detectInitSystem(),
        shell: await this.detectShell()
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Fallback detection methods
   */
  private async detectFallback(): Promise<DistributionInfo> {
    let name: SystemDistribution = 'ubuntu'; // Default fallback
    let version = 'unknown';

    try {
      // Try lsb_release
      const { stdout } = await execAsync('lsb_release -si 2>/dev/null || echo ""');
      if (stdout.trim()) {
        name = this.normalizeDistributionName(stdout.trim());
      }

      const { stdout: versionOut } = await execAsync('lsb_release -sr 2>/dev/null || echo "unknown"');
      version = versionOut.trim();
    } catch (error) {
      // Try checking for specific files
      try {
        await access('/etc/alpine-release');
        name = 'alpine';
        const alpineVersion = await readFile('/etc/alpine-release', 'utf-8');
        version = alpineVersion.trim();
      } catch (alpineError) {
        try {
          await access('/etc/debian_version');
          name = 'ubuntu'; // Assume Ubuntu if Debian-based
          const debianVersion = await readFile('/etc/debian_version', 'utf-8');
          version = debianVersion.trim();
        } catch (debianError) {
          // Final fallback
          console.warn('Unable to detect distribution, defaulting to Ubuntu');
        }
      }
    }

    return {
      name,
      version,
      serviceManager: await this.detectServiceManager(),
      packageManager: this.getPackageManager(name),
      initSystem: await this.detectInitSystem(),
      shell: await this.detectShell()
    };
  }

  /**
   * Normalize distribution name to supported types
   */
  private normalizeDistributionName(name: string): SystemDistribution {
    const normalized = name.toLowerCase();
    
    if (normalized.includes('alpine')) {
      return 'alpine';
    }
    
    if (normalized.includes('ubuntu') || normalized.includes('debian')) {
      return 'ubuntu';
    }

    // Default to Ubuntu for unknown distributions
    return 'ubuntu';
  }

  /**
   * Detect service manager (systemd or openrc)
   */
  private async detectServiceManager(): Promise<ServiceManager> {
    try {
      // Check if systemd is running
      await execAsync('systemctl --version');
      return 'systemd';
    } catch (error) {
      try {
        // Check if openrc is available
        await execAsync('rc-status --version');
        return 'openrc';
      } catch (openrcError) {
        // Default to systemd for most modern distributions
        return 'systemd';
      }
    }
  }

  /**
   * Get package manager based on distribution
   */
  private getPackageManager(distribution: SystemDistribution): PackageManager {
    switch (distribution) {
      case 'alpine':
        return 'apk';
      case 'ubuntu':
      default:
        return 'apt';
    }
  }

  /**
   * Detect init system
   */
  private async detectInitSystem(): Promise<string> {
    try {
      const { stdout } = await execAsync('ps -p 1 -o comm= 2>/dev/null || echo "unknown"');
      return stdout.trim() || 'unknown';
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * Detect default shell
   */
  private async detectShell(): Promise<string> {
    try {
      const { stdout } = await execAsync('echo $SHELL 2>/dev/null || echo "/bin/sh"');
      return stdout.trim() || '/bin/sh';
    } catch (error) {
      return '/bin/sh';
    }
  }

  /**
   * Check if a command is available
   */
  public async isCommandAvailable(command: string): Promise<boolean> {
    try {
      await execAsync(`which ${command}`);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get system architecture
   */
  public async getArchitecture(): Promise<string> {
    try {
      const { stdout } = await execAsync('uname -m');
      return stdout.trim();
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * Get kernel version
   */
  public async getKernelVersion(): Promise<string> {
    try {
      const { stdout } = await execAsync('uname -r');
      return stdout.trim();
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * Get hostname
   */
  public async getHostname(): Promise<string> {
    try {
      const { stdout } = await execAsync('hostname');
      return stdout.trim();
    } catch (error) {
      return 'localhost';
    }
  }

  /**
   * Get system uptime in seconds
   */
  public async getUptime(): Promise<number> {
    try {
      const { stdout } = await execAsync('cat /proc/uptime');
      const uptimeStr = stdout.split(' ')[0];
      return parseFloat(uptimeStr);
    } catch (error) {
      return 0;
    }
  }

  /**
   * Get load average
   */
  public async getLoadAverage(): Promise<number[]> {
    try {
      const { stdout } = await execAsync('cat /proc/loadavg');
      const parts = stdout.trim().split(' ');
      return [
        parseFloat(parts[0]) || 0,
        parseFloat(parts[1]) || 0,
        parseFloat(parts[2]) || 0
      ];
    } catch (error) {
      return [0, 0, 0];
    }
  }

  /**
   * Reset cached distribution info (for testing)
   */
  public resetCache(): void {
    this.distributionInfo = null;
  }
}
