/**
 * Security Validator
 * 
 * Validates commands and operations for security compliance
 */

import { SystemCommand, SecurityPolicy } from '../types';

export class SecurityValidator {
  private policy: SecurityPolicy;

  // Default dangerous commands that should be blocked
  private readonly DEFAULT_BLOCKED_COMMANDS = [
    'rm -rf /',
    'dd if=/dev/zero',
    'mkfs',
    'fdisk',
    'parted',
    'format',
    'shutdown',
    'reboot',
    'halt',
    'poweroff',
    'init 0',
    'init 6',
    'telinit',
    'wall',
    'write',
    'mesg',
    'su -',
    'sudo su',
    'passwd',
    'chpasswd',
    'usermod',
    'userdel',
    'groupdel',
    'deluser',
    'delgroup'
  ];

  // Default dangerous paths
  private readonly DEFAULT_BLOCKED_PATHS = [
    '/etc/passwd',
    '/etc/shadow',
    '/etc/group',
    '/etc/sudoers',
    '/boot',
    '/sys',
    '/proc/sys',
    '/dev/mem',
    '/dev/kmem',
    '/dev/port'
  ];

  // Commands that typically require elevated privileges
  private readonly PRIVILEGED_COMMANDS = [
    'mount',
    'umount',
    'iptables',
    'ip6tables',
    'systemctl',
    'service',
    'rc-service',
    'rc-update',
    'update-rc.d',
    'chkconfig',
    'modprobe',
    'rmmod',
    'insmod',
    'lsmod',
    'sysctl',
    'crontab',
    'at',
    'batch'
  ];

  constructor(policy?: SecurityPolicy) {
    this.policy = {
      allowedUsers: [],
      allowedCommands: [],
      blockedCommands: [...this.DEFAULT_BLOCKED_COMMANDS],
      requireSudo: false,
      maxCommandLength: 1000,
      allowFileAccess: true,
      allowedPaths: [],
      blockedPaths: [...this.DEFAULT_BLOCKED_PATHS],
      enableAuditLog: true,
      ...policy
    };
  }

  /**
   * Validate a system command for security compliance
   */
  public async validateCommand(command: SystemCommand): Promise<void> {
    // Check command length
    const fullCommand = this.buildFullCommand(command);
    if (fullCommand.length > (this.policy.maxCommandLength || 1000)) {
      throw new Error('Command exceeds maximum allowed length');
    }

    // Check for blocked commands
    this.validateBlockedCommands(fullCommand);

    // Check for allowed commands (if whitelist is defined)
    this.validateAllowedCommands(command.command);

    // Check for dangerous patterns
    this.validateDangerousPatterns(fullCommand);

    // Check file access permissions
    this.validateFileAccess(command);

    // Check user permissions
    this.validateUserPermissions(command);

    // Check for privilege escalation attempts
    this.validatePrivilegeEscalation(command);

    // Validate paths in arguments
    this.validatePaths(command.args);
  }

  /**
   * Validate against blocked commands
   */
  private validateBlockedCommands(fullCommand: string): void {
    const blockedCommands = this.policy.blockedCommands || [];
    
    for (const blocked of blockedCommands) {
      if (fullCommand.includes(blocked)) {
        throw new Error(`Blocked command detected: ${blocked}`);
      }
    }

    // Check for command injection patterns
    const injectionPatterns = [
      /;\s*rm\s+-rf/,
      /\|\s*rm\s+-rf/,
      /&&\s*rm\s+-rf/,
      /;\s*dd\s+if=/,
      /\|\s*dd\s+if=/,
      /&&\s*dd\s+if=/,
      /;\s*mkfs/,
      /\|\s*mkfs/,
      /&&\s*mkfs/
    ];

    for (const pattern of injectionPatterns) {
      if (pattern.test(fullCommand)) {
        throw new Error('Potentially dangerous command injection detected');
      }
    }
  }

  /**
   * Validate against allowed commands whitelist
   */
  private validateAllowedCommands(command: string): void {
    const allowedCommands = this.policy.allowedCommands;
    
    if (allowedCommands && allowedCommands.length > 0) {
      const isAllowed = allowedCommands.some(allowed => {
        // Support wildcards
        if (allowed.includes('*')) {
          const regex = new RegExp(allowed.replace(/\*/g, '.*'));
          return regex.test(command);
        }
        return command === allowed;
      });

      if (!isAllowed) {
        throw new Error(`Command not in allowed list: ${command}`);
      }
    }
  }

  /**
   * Validate for dangerous patterns
   */
  private validateDangerousPatterns(fullCommand: string): void {
    const dangerousPatterns = [
      // File system manipulation
      /rm\s+-rf\s+\/[^\/\s]*/,
      /rm\s+-rf\s+\*/,
      /find\s+\/.*-delete/,
      /find\s+\/.*-exec\s+rm/,
      
      // Network manipulation
      /iptables\s+-F/,
      /iptables\s+-X/,
      /ip\s+route\s+del/,
      
      // System manipulation
      /echo\s+.*>\s*\/etc\//,
      /cat\s+.*>\s*\/etc\//,
      />\s*\/etc\//,
      />>\s*\/etc\//,
      
      // Process manipulation
      /kill\s+-9\s+1/,
      /killall\s+-9/,
      
      // Privilege escalation
      /sudo\s+su/,
      /su\s+-/,
      /chmod\s+777\s+\/etc/,
      /chown\s+.*\/etc/,
      
      // Data exfiltration
      /curl\s+.*\|\s*sh/,
      /wget\s+.*\|\s*sh/,
      /nc\s+.*\|\s*sh/,
      
      // System information gathering (potentially malicious)
      /cat\s+\/etc\/passwd/,
      /cat\s+\/etc\/shadow/,
      /cat\s+\/proc\/version/,
      /uname\s+-a.*\|\s*nc/
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(fullCommand)) {
        throw new Error('Potentially dangerous command pattern detected');
      }
    }
  }

  /**
   * Validate file access permissions
   */
  private validateFileAccess(command: SystemCommand): void {
    if (!this.policy.allowFileAccess) {
      // Check if command involves file operations
      const fileCommands = ['cat', 'less', 'more', 'head', 'tail', 'grep', 'find', 'locate', 'ls', 'cp', 'mv', 'rm', 'mkdir', 'rmdir', 'touch', 'chmod', 'chown'];
      
      if (fileCommands.includes(command.command)) {
        throw new Error('File access is not allowed');
      }
    }
  }

  /**
   * Validate user permissions
   */
  private validateUserPermissions(command: SystemCommand): void {
    const allowedUsers = this.policy.allowedUsers;
    
    if (allowedUsers && allowedUsers.length > 0 && command.user) {
      if (!allowedUsers.includes(command.user)) {
        throw new Error(`User not allowed: ${command.user}`);
      }
    }
  }

  /**
   * Validate privilege escalation attempts
   */
  private validatePrivilegeEscalation(command: SystemCommand): void {
    // Check if command requires privileges but sudo is not explicitly requested
    if (this.PRIVILEGED_COMMANDS.includes(command.command) && !command.sudo) {
      if (this.policy.requireSudo) {
        throw new Error(`Command requires sudo: ${command.command}`);
      }
    }

    // Check for suspicious sudo usage
    if (command.sudo) {
      const suspiciousSudoPatterns = [
        /sudo\s+su/,
        /sudo\s+bash/,
        /sudo\s+sh/,
        /sudo\s+zsh/,
        /sudo\s+fish/,
        /sudo\s+passwd/,
        /sudo\s+visudo/
      ];

      const fullCommand = this.buildFullCommand(command);
      for (const pattern of suspiciousSudoPatterns) {
        if (pattern.test(fullCommand)) {
          throw new Error('Suspicious sudo usage detected');
        }
      }
    }
  }

  /**
   * Validate paths in command arguments
   */
  private validatePaths(args: string[]): void {
    const blockedPaths = this.policy.blockedPaths || [];
    const allowedPaths = this.policy.allowedPaths || [];

    for (const arg of args) {
      // Check if argument looks like a path
      if (arg.startsWith('/') || arg.startsWith('./') || arg.startsWith('../')) {
        // Check against blocked paths
        for (const blockedPath of blockedPaths) {
          if (arg.startsWith(blockedPath)) {
            throw new Error(`Access to blocked path: ${arg}`);
          }
        }

        // Check against allowed paths (if whitelist is defined)
        if (allowedPaths.length > 0) {
          const isAllowed = allowedPaths.some(allowedPath => 
            arg.startsWith(allowedPath)
          );
          
          if (!isAllowed) {
            throw new Error(`Access to path not allowed: ${arg}`);
          }
        }
      }
    }
  }

  /**
   * Build full command string for validation
   */
  private buildFullCommand(command: SystemCommand): string {
    return `${command.command} ${command.args.join(' ')}`.trim();
  }

  /**
   * Update security policy
   */
  public updatePolicy(policy: Partial<SecurityPolicy>): void {
    this.policy = { ...this.policy, ...policy };
  }

  /**
   * Get current security policy
   */
  public getPolicy(): SecurityPolicy {
    return { ...this.policy };
  }

  /**
   * Check if a command is potentially dangerous
   */
  public isDangerous(command: string): boolean {
    try {
      const systemCommand: SystemCommand = {
        id: 'test',
        command: command.split(' ')[0],
        args: command.split(' ').slice(1)
      };
      
      this.validateCommand(systemCommand);
      return false;
    } catch (error) {
      return true;
    }
  }

  /**
   * Sanitize command arguments
   */
  public sanitizeArgs(args: string[]): string[] {
    return args.map(arg => {
      // Remove potentially dangerous characters
      return arg.replace(/[;&|`$(){}[\]<>]/g, '');
    });
  }
}
