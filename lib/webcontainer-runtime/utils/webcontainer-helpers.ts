/**
 * WebContainer Utility Helpers
 * 
 * Utility functions for WebContainer operations
 */

import { FileSystemTree } from '@webcontainer/api';
import {
  WebContainerTemplate,
  WebContainerConfig,
  FileEntry,
  ProcessInfo
} from '../types';

/**
 * Create a WebContainer template
 */
export function createWebContainerTemplate(
  id: string,
  name: string,
  description: string,
  category: string,
  files: FileSystemTree,
  config: Partial<WebContainerConfig> = {},
  options: {
    dependencies?: string[];
    scripts?: Record<string, string>;
    tags?: string[];
  } = {}
): WebContainerTemplate {
  return {
    id,
    name,
    description,
    category,
    files,
    config,
    dependencies: options.dependencies || [],
    scripts: options.scripts || {},
    tags: options.tags || []
  };
}

/**
 * Get default project templates
 */
export function getDefaultTemplates(): WebContainerTemplate[] {
  return [
    createReactTemplate(),
    createNodeTemplate(),
    createNextJSTemplate(),
    createVueTemplate(),
    createSvelteTemplate()
  ];
}

/**
 * Create React template
 */
function createReactTemplate(): WebContainerTemplate {
  return createWebContainerTemplate(
    'react-vite',
    'React + Vite',
    'React application with Vite bundler',
    'frontend',
    {
      'package.json': {
        file: {
          contents: JSON.stringify({
            name: 'react-app',
            version: '1.0.0',
            type: 'module',
            scripts: {
              dev: 'vite',
              build: 'vite build',
              preview: 'vite preview'
            },
            dependencies: {
              react: '^18.2.0',
              'react-dom': '^18.2.0'
            },
            devDependencies: {
              '@vitejs/plugin-react': '^4.0.0',
              vite: '^4.0.0'
            }
          }, null, 2)
        }
      },
      'index.html': {
        file: {
          contents: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>React App</title>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/main.jsx"></script>
</body>
</html>`
        }
      },
      'src': {
        directory: {
          'main.jsx': {
            file: {
              contents: `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)`
            }
          },
          'App.jsx': {
            file: {
              contents: `import React from 'react'

function App() {
  return (
    <div>
      <h1>Hello WebContainer!</h1>
      <p>React app running in WebContainer</p>
    </div>
  )
}

export default App`
            }
          }
        }
      },
      'vite.config.js': {
        file: {
          contents: `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    host: true,
    port: 3000
  }
})`
        }
      }
    },
    { name: 'react-app' },
    {
      dependencies: ['react', 'react-dom', 'vite'],
      scripts: { dev: 'npm run dev', build: 'npm run build' },
      tags: ['react', 'vite', 'frontend']
    }
  );
}

/**
 * Create Node.js template
 */
function createNodeTemplate(): WebContainerTemplate {
  return createWebContainerTemplate(
    'node-express',
    'Node.js + Express',
    'Node.js server with Express framework',
    'backend',
    {
      'package.json': {
        file: {
          contents: JSON.stringify({
            name: 'node-app',
            version: '1.0.0',
            type: 'module',
            scripts: {
              start: 'node server.js',
              dev: 'node --watch server.js'
            },
            dependencies: {
              express: '^4.18.0'
            }
          }, null, 2)
        }
      },
      'server.js': {
        file: {
          contents: `import express from 'express';

const app = express();
const port = 3000;

app.use(express.json());

app.get('/', (req, res) => {
  res.json({ message: 'Hello from WebContainer!' });
});

app.listen(port, () => {
  console.log(\`Server running at http://localhost:\${port}\`);
});`
        }
      }
    },
    { name: 'node-app' },
    {
      dependencies: ['express'],
      scripts: { start: 'npm start', dev: 'npm run dev' },
      tags: ['node', 'express', 'backend']
    }
  );
}

/**
 * Create Next.js template
 */
function createNextJSTemplate(): WebContainerTemplate {
  return createWebContainerTemplate(
    'nextjs',
    'Next.js',
    'Next.js React framework application',
    'fullstack',
    {
      'package.json': {
        file: {
          contents: JSON.stringify({
            name: 'nextjs-app',
            version: '1.0.0',
            scripts: {
              dev: 'next dev',
              build: 'next build',
              start: 'next start'
            },
            dependencies: {
              next: '^14.0.0',
              react: '^18.2.0',
              'react-dom': '^18.2.0'
            }
          }, null, 2)
        }
      },
      'app': {
        directory: {
          'page.js': {
            file: {
              contents: `export default function Home() {
  return (
    <div>
      <h1>Hello Next.js in WebContainer!</h1>
      <p>This is a Next.js app running in WebContainer</p>
    </div>
  )
}`
            }
          },
          'layout.js': {
            file: {
              contents: `export const metadata = {
  title: 'Next.js App',
  description: 'Next.js app in WebContainer',
}

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}`
            }
          }
        }
      }
    },
    { name: 'nextjs-app' },
    {
      dependencies: ['next', 'react', 'react-dom'],
      scripts: { dev: 'npm run dev', build: 'npm run build' },
      tags: ['nextjs', 'react', 'fullstack']
    }
  );
}

/**
 * Create Vue template
 */
function createVueTemplate(): WebContainerTemplate {
  return createWebContainerTemplate(
    'vue-vite',
    'Vue + Vite',
    'Vue.js application with Vite',
    'frontend',
    {
      'package.json': {
        file: {
          contents: JSON.stringify({
            name: 'vue-app',
            version: '1.0.0',
            type: 'module',
            scripts: {
              dev: 'vite',
              build: 'vite build',
              preview: 'vite preview'
            },
            dependencies: {
              vue: '^3.3.0'
            },
            devDependencies: {
              '@vitejs/plugin-vue': '^4.0.0',
              vite: '^4.0.0'
            }
          }, null, 2)
        }
      },
      'index.html': {
        file: {
          contents: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Vue App</title>
</head>
<body>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
</body>
</html>`
        }
      },
      'src': {
        directory: {
          'main.js': {
            file: {
              contents: `import { createApp } from 'vue'
import App from './App.vue'

createApp(App).mount('#app')`
            }
          },
          'App.vue': {
            file: {
              contents: `<template>
  <div>
    <h1>Hello Vue in WebContainer!</h1>
    <p>Vue.js app running in WebContainer</p>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>`
            }
          }
        }
      },
      'vite.config.js': {
        file: {
          contents: `import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    host: true,
    port: 3000
  }
})`
        }
      }
    },
    { name: 'vue-app' },
    {
      dependencies: ['vue'],
      scripts: { dev: 'npm run dev', build: 'npm run build' },
      tags: ['vue', 'vite', 'frontend']
    }
  );
}

/**
 * Create Svelte template
 */
function createSvelteTemplate(): WebContainerTemplate {
  return createWebContainerTemplate(
    'svelte-vite',
    'Svelte + Vite',
    'Svelte application with Vite',
    'frontend',
    {
      'package.json': {
        file: {
          contents: JSON.stringify({
            name: 'svelte-app',
            version: '1.0.0',
            type: 'module',
            scripts: {
              dev: 'vite',
              build: 'vite build',
              preview: 'vite preview'
            },
            dependencies: {
              svelte: '^4.0.0'
            },
            devDependencies: {
              '@sveltejs/vite-plugin-svelte': '^2.0.0',
              vite: '^4.0.0'
            }
          }, null, 2)
        }
      },
      'index.html': {
        file: {
          contents: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Svelte App</title>
</head>
<body>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
</body>
</html>`
        }
      },
      'src': {
        directory: {
          'main.js': {
            file: {
              contents: `import App from './App.svelte'

const app = new App({
  target: document.getElementById('app'),
})

export default app`
            }
          },
          'App.svelte': {
            file: {
              contents: `<script>
  let name = 'WebContainer'
</script>

<main>
  <h1>Hello {name}!</h1>
  <p>Svelte app running in WebContainer</p>
</main>`
            }
          }
        }
      },
      'vite.config.js': {
        file: {
          contents: `import { defineConfig } from 'vite'
import { svelte } from '@sveltejs/vite-plugin-svelte'

export default defineConfig({
  plugins: [svelte()],
  server: {
    host: true,
    port: 3000
  }
})`
        }
      }
    },
    { name: 'svelte-app' },
    {
      dependencies: ['svelte'],
      scripts: { dev: 'npm run dev', build: 'npm run build' },
      tags: ['svelte', 'vite', 'frontend']
    }
  );
}

/**
 * Convert file tree to FileSystemTree format
 */
export function convertToFileSystemTree(files: FileEntry[]): FileSystemTree {
  const tree: FileSystemTree = {};

  for (const file of files) {
    const pathParts = file.path.split('/').filter(Boolean);
    let current = tree;

    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];
      const isLast = i === pathParts.length - 1;

      if (isLast) {
        if (file.type === 'file') {
          current[part] = {
            file: {
              contents: '' // Content would need to be provided separately
            }
          };
        } else {
          current[part] = {};
        }
      } else {
        if (!current[part]) {
          current[part] = {};
        }
        current = current[part] as FileSystemTree;
      }
    }
  }

  return tree;
}

/**
 * Extract file paths from FileSystemTree
 */
export function extractFilePaths(tree: FileSystemTree, basePath: string = ''): string[] {
  const paths: string[] = [];

  for (const [name, content] of Object.entries(tree)) {
    const fullPath = basePath ? `${basePath}/${name}` : name;

    if (content && typeof content === 'object' && 'file' in content) {
      paths.push(fullPath);
    } else if (content && typeof content === 'object') {
      paths.push(...extractFilePaths(content as FileSystemTree, fullPath));
    }
  }

  return paths;
}

/**
 * Validate process output for common patterns
 */
export function analyzeProcessOutput(processInfo: ProcessInfo, output: string[]): {
  isServerRunning: boolean;
  serverUrl?: string;
  hasErrors: boolean;
  errors: string[];
  isComplete: boolean;
} {
  const fullOutput = output.join('\n');
  
  // Check for server running patterns
  const serverPatterns = [
    /server.*running.*(?:at|on).*(?:http:\/\/)?(?:localhost:)?(\d+)/i,
    /local:.*http:\/\/localhost:(\d+)/i,
    /dev server running at.*(\d+)/i
  ];

  let serverUrl: string | undefined;
  let isServerRunning = false;

  for (const pattern of serverPatterns) {
    const match = fullOutput.match(pattern);
    if (match) {
      const port = match[1];
      serverUrl = `http://localhost:${port}`;
      isServerRunning = true;
      break;
    }
  }

  // Check for errors
  const errorPatterns = [
    /error:/i,
    /failed/i,
    /cannot find module/i,
    /syntax error/i,
    /reference error/i,
    /type error/i
  ];

  const errors: string[] = [];
  const hasErrors = errorPatterns.some(pattern => {
    if (pattern.test(fullOutput)) {
      const lines = fullOutput.split('\n');
      const errorLines = lines.filter(line => pattern.test(line));
      errors.push(...errorLines);
      return true;
    }
    return false;
  });

  // Check if process is complete
  const isComplete = processInfo.status === 'completed' || processInfo.status === 'failed';

  return {
    isServerRunning,
    serverUrl,
    hasErrors,
    errors,
    isComplete
  };
}

/**
 * Generate container name from project info
 */
export function generateContainerName(projectId?: string, template?: string): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  
  if (projectId) {
    return `${projectId}-${timestamp}`;
  }
  
  if (template) {
    return `${template}-${timestamp}-${random}`;
  }
  
  return `container-${timestamp}-${random}`;
}

/**
 * Sanitize file path for WebContainer
 */
export function sanitizeFilePath(path: string): string {
  return path
    .replace(/\\/g, '/') // Convert Windows paths
    .replace(/\/+/g, '/') // Remove duplicate slashes
    .replace(/^\//, '') // Remove leading slash
    .replace(/\/$/, ''); // Remove trailing slash
}
