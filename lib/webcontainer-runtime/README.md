# WebContainer Runtime Library

A comprehensive TypeScript library for WebContainer-based execution of Node.js applications, providing seamless integration with the existing workspace system.

## Overview

This library provides a production-ready WebContainer runtime that enables running Node.js applications directly in the browser using StackBlitz's WebContainer API. It follows the established patterns from the Nodebox integration and provides a modular architecture for container lifecycle management, filesystem operations, and process execution.

## Features

### Core Capabilities
- **WebContainer Lifecycle Management**: Create, start, stop, and destroy WebContainer instances
- **Filesystem Operations**: Read, write, delete files with bidirectional synchronization
- **Process Management**: Execute commands, monitor processes, stream output in real-time
- **Template System**: Pre-configured project templates for React, Vue, Svelte, Next.js, and Node.js
- **Real-time Monitoring**: Process output streaming and container state management
- **Error Handling**: Comprehensive error handling with retry mechanisms and graceful fallbacks

### Integration Features
- **Workspace Integration**: Seamless integration with `ai-nodejs-workspace-layout.tsx`
- **Tab System**: Compatible with existing tab-based workspace architecture
- **State Management**: Zustand store following established patterns
- **API Routes**: RESTful API endpoints for container operations
- **TypeScript Support**: Full TypeScript definitions and type safety

## Architecture

```
lib/webcontainer-runtime/
├── core/                    # Core runtime management
│   ├── webcontainer-manager.ts   # Main container lifecycle manager
│   ├── filesystem-manager.ts     # File operations and sync
│   └── process-manager.ts        # Process execution and monitoring
├── api/                     # API layer
│   └── webcontainer-client.ts    # HTTP client for API communication
├── hooks/                   # React hooks
│   └── use-webcontainer.ts       # React integration hooks
├── types/                   # TypeScript definitions
│   └── index.ts                  # All type definitions
├── utils/                   # Utilities
│   └── webcontainer-helpers.ts   # Helper functions and templates
└── index.ts                 # Main exports
```

## Quick Start

### 1. Basic Usage

```typescript
import { useWebContainer } from '@/lib/webcontainer-runtime';

function MyComponent() {
  const {
    container,
    createContainer,
    executeCommand,
    readFile,
    writeFile
  } = useWebContainer();

  const handleCreateReactApp = async () => {
    const containerId = await createContainer({
      name: 'my-react-app',
      template: 'react-vite'
    });
    
    // Start development server
    await executeCommand('npm', ['run', 'dev']);
  };

  return (
    <div>
      <button onClick={handleCreateReactApp}>
        Create React App
      </button>
    </div>
  );
}
```

### 2. Using Templates

```typescript
import { useWebContainerStore } from '@/lib/stores/webcontainer-store';

function TemplateSelector() {
  const { createFromTemplate, getTemplates } = useWebContainerStore();
  const templates = getTemplates();

  const handleCreateFromTemplate = async (templateId: string) => {
    const containerId = await createFromTemplate(templateId, {
      name: 'my-new-project',
      projectId: 'project-123'
    });
    console.log('Created container:', containerId);
  };

  return (
    <div>
      {templates.map(template => (
        <button
          key={template.id}
          onClick={() => handleCreateFromTemplate(template.id)}
        >
          {template.name}
        </button>
      ))}
    </div>
  );
}
```

### 3. Workspace Integration

```typescript
import { useWebContainerIntegration } from '@/lib/stores/webcontainer-store';

function WorkspaceLayout({ projectId }: { projectId: string }) {
  const {
    containers,
    activeContainer,
    isLoading,
    error
  } = useWebContainerIntegration(projectId);

  if (isLoading) return <div>Loading containers...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <h2>Active Container: {activeContainer?.name}</h2>
      <div>
        {containers.map(container => (
          <div key={container.id}>
            {container.name} - {container.status}
          </div>
        ))}
      </div>
    </div>
  );
}
```

## API Reference

### Core Classes

#### WebContainerManager
Main class for container lifecycle management.

```typescript
const manager = new WebContainerManager();
await manager.initialize();

// Create container
const containerId = await manager.createContainer(config, files);

// Execute command
const process = await manager.executeCommand(containerId, 'npm', ['install']);

// File operations
await manager.writeFile(containerId, 'package.json', content);
const content = await manager.readFile(containerId, 'package.json');
```

#### FilesystemManager
Handles file operations and synchronization.

```typescript
const fsManager = new FilesystemManager(webContainerManager);

// Mount files
await fsManager.mountFiles(containerId, fileSystemTree);

// Get file tree
const files = await fsManager.getFileTree(containerId);

// Start watching for changes
await fsManager.startWatching(containerId, { watchChanges: true });
```

#### ProcessManager
Manages process execution and monitoring.

```typescript
const processManager = new ProcessManager(webContainerManager);

// Execute command
const process = await processManager.executeCommand(
  containerId,
  'npm',
  ['run', 'dev'],
  { cwd: '/app' }
);

// Start dev server
const devProcess = await processManager.startDevServer(containerId);

// Install packages
const installProcess = await processManager.installPackages(
  containerId,
  ['react', 'typescript']
);
```

### React Hooks

#### useWebContainer
Main hook for WebContainer operations.

```typescript
const {
  container,           // Current container instance
  isLoading,          // Loading state
  error,              // Error state
  createContainer,    // Create new container
  executeCommand,     // Execute command
  readFile,           // Read file
  writeFile,          // Write file
  deleteFile,         // Delete file
  listFiles           // List files
} = useWebContainer({ containerId: 'optional-id' });
```

#### useWebContainerManager
Hook for managing multiple containers.

```typescript
const {
  containers,         // All containers
  createContainer,    // Create new container
  destroyContainer,   // Destroy container
  refreshContainers   // Refresh container list
} = useWebContainerManager();
```

### API Endpoints

The library provides RESTful API endpoints:

- `GET /api/webcontainer-api` - List all containers
- `POST /api/webcontainer-api` - Create new container
- `GET /api/webcontainer-api/[id]` - Get container details
- `DELETE /api/webcontainer-api/[id]` - Destroy container
- `GET /api/webcontainer-api/[id]/filesystem` - File operations
- `POST /api/webcontainer-api/[id]/processes` - Process operations

## Templates

### Available Templates

1. **React + Vite** (`react-vite`)
   - React 18 with Vite bundler
   - Hot reload and fast refresh
   - Modern development setup

2. **Node.js + Express** (`node-express`)
   - Express server with ES modules
   - Basic API setup
   - Development with --watch flag

3. **Next.js** (`nextjs`)
   - Next.js 14 with App Router
   - React Server Components
   - Full-stack capabilities

4. **Vue + Vite** (`vue-vite`)
   - Vue 3 with Composition API
   - Vite for fast development
   - Single File Components

5. **Svelte + Vite** (`svelte-vite`)
   - Svelte 4 with Vite
   - Reactive programming
   - Minimal bundle size

### Creating Custom Templates

```typescript
import { createWebContainerTemplate } from '@/lib/webcontainer-runtime';

const customTemplate = createWebContainerTemplate(
  'my-template',
  'My Custom Template',
  'Description of the template',
  'category',
  fileSystemTree,
  { name: 'default-name' },
  {
    dependencies: ['package1', 'package2'],
    scripts: { dev: 'npm run dev' },
    tags: ['custom', 'template']
  }
);
```

## Error Handling

The library provides comprehensive error handling:

```typescript
try {
  await createContainer(config);
} catch (error) {
  if (error.code === 'CONTAINER_CREATION_FAILED') {
    // Handle container creation failure
  } else if (error.code === 'FILESYSTEM_ERROR') {
    // Handle filesystem error
  }
}
```

## Integration with Workspace

The WebContainer runtime integrates seamlessly with the existing workspace system:

1. **Tab Integration**: Containers appear as tabs in the workspace
2. **State Management**: Uses Zustand store following established patterns
3. **Event System**: Real-time updates and notifications
4. **File Synchronization**: Bidirectional sync with workspace files

## Performance Considerations

- **Memory Management**: Automatic cleanup of inactive containers
- **Output Buffering**: Limited process output to prevent memory leaks
- **Lazy Loading**: Containers are created on-demand
- **Resource Limits**: Configurable CPU and memory limits

## Browser Compatibility

- **Modern Browsers**: Chrome 88+, Firefox 85+, Safari 14+
- **WebAssembly**: Required for WebContainer runtime
- **SharedArrayBuffer**: Required for multi-threading support

## Limitations

- **Node.js Modules**: Limited to browser-compatible modules
- **File System**: Virtual filesystem, no direct local access
- **Network**: Limited network access compared to Node.js
- **Performance**: Slower than native Node.js execution

## Contributing

When contributing to the WebContainer runtime:

1. Follow the established patterns from Nodebox integration
2. Maintain TypeScript type safety
3. Add comprehensive error handling
4. Include tests for new functionality
5. Update documentation for API changes

## Dependencies

- `@webcontainer/api`: Core WebContainer runtime
- `zustand`: State management
- `immer`: Immutable state updates
- React 18+ for UI components
- Next.js for API routes

## License

This library is part of the larger application and follows the same licensing terms.
