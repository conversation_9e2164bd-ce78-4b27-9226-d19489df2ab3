/**
 * WebContainer Workspace Component
 * 
 * Example component demonstrating WebContainer integration
 */

"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Play, 
  Square, 
  Trash2, 
  FileText, 
  Terminal, 
  Loader2,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { useWebContainerStore } from '@/lib/stores/webcontainer-store';
import { WebContainerTemplate } from '@/lib/webcontainer-runtime/types';

interface WebContainerWorkspaceProps {
  projectId?: string;
  className?: string;
}

export function WebContainerWorkspace({ 
  projectId, 
  className 
}: WebContainerWorkspaceProps) {
  const {
    containers,
    activeContainerId,
    isLoading,
    error,
    initializeClient,
    createFromTemplate,
    startContainer,
    stopContainer,
    destroyContainer,
    setActiveContainer,
    getTemplates,
    executeCommand,
    processes,
    processOutput
  } = useWebContainerStore();

  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [containerName, setContainerName] = useState('');
  const [activeTab, setActiveTab] = useState('containers');

  const templates = getTemplates();
  const activeContainer = containers.find(c => c.id === activeContainerId);
  const containerProcesses = activeContainerId ? processes[activeContainerId] || [] : [];

  // Initialize client on mount
  useEffect(() => {
    initializeClient();
  }, [initializeClient]);

  const handleCreateContainer = async () => {
    if (!selectedTemplate || !containerName) return;

    try {
      const containerId = await createFromTemplate(selectedTemplate, {
        name: containerName,
        projectId
      });
      
      setContainerName('');
      setSelectedTemplate('');
      setActiveTab('containers');
    } catch (error) {
      console.error('Failed to create container:', error);
    }
  };

  const handleStartContainer = async (containerId: string) => {
    try {
      await startContainer(containerId);
    } catch (error) {
      console.error('Failed to start container:', error);
    }
  };

  const handleStopContainer = async (containerId: string) => {
    try {
      await stopContainer(containerId);
    } catch (error) {
      console.error('Failed to stop container:', error);
    }
  };

  const handleDestroyContainer = async (containerId: string) => {
    try {
      await destroyContainer(containerId);
    } catch (error) {
      console.error('Failed to destroy container:', error);
    }
  };

  const handleRunCommand = async (command: string) => {
    if (!activeContainerId) return;

    try {
      await executeCommand(activeContainerId, command.split(' ')[0], command.split(' ').slice(1));
    } catch (error) {
      console.error('Failed to execute command:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-500';
      case 'ready': return 'bg-blue-500';
      case 'stopped': return 'bg-gray-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-yellow-500';
    }
  };

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            WebContainer Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600">{error.message}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="containers">Containers</TabsTrigger>
          <TabsTrigger value="create">Create</TabsTrigger>
          <TabsTrigger value="files">Files</TabsTrigger>
          <TabsTrigger value="terminal">Terminal</TabsTrigger>
        </TabsList>

        <TabsContent value="containers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>WebContainer Instances</CardTitle>
              <CardDescription>
                Manage your WebContainer instances
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : containers.length === 0 ? (
                <p className="text-muted-foreground text-center p-8">
                  No containers created yet. Create one from the templates.
                </p>
              ) : (
                <div className="space-y-4">
                  {containers.map((container) => (
                    <Card 
                      key={container.id}
                      className={`cursor-pointer transition-colors ${
                        activeContainerId === container.id ? 'ring-2 ring-primary' : ''
                      }`}
                      onClick={() => setActiveContainer(container.id)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full ${getStatusColor(container.status)}`} />
                            <div>
                              <h3 className="font-medium">{container.name}</h3>
                              <p className="text-sm text-muted-foreground">
                                {container.id}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{container.status}</Badge>
                            <div className="flex gap-1">
                              {container.status === 'ready' || container.status === 'stopped' ? (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleStartContainer(container.id);
                                  }}
                                >
                                  <Play className="h-4 w-4" />
                                </Button>
                              ) : (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleStopContainer(container.id);
                                  }}
                                >
                                  <Square className="h-4 w-4" />
                                </Button>
                              )}
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDestroyContainer(container.id);
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="create" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Create New Container</CardTitle>
              <CardDescription>
                Choose a template and create a new WebContainer instance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Container Name</label>
                <input
                  type="text"
                  value={containerName}
                  onChange={(e) => setContainerName(e.target.value)}
                  placeholder="my-awesome-app"
                  className="w-full mt-1 px-3 py-2 border rounded-md"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Template</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                  {templates.map((template) => (
                    <Card
                      key={template.id}
                      className={`cursor-pointer transition-colors ${
                        selectedTemplate === template.id ? 'ring-2 ring-primary' : ''
                      }`}
                      onClick={() => setSelectedTemplate(template.id)}
                    >
                      <CardContent className="p-4">
                        <h3 className="font-medium">{template.name}</h3>
                        <p className="text-sm text-muted-foreground mt-1">
                          {template.description}
                        </p>
                        <div className="flex gap-1 mt-2">
                          {template.tags.map((tag) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              <Button
                onClick={handleCreateContainer}
                disabled={!selectedTemplate || !containerName || isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                Create Container
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="files" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                File Explorer
              </CardTitle>
            </CardHeader>
            <CardContent>
              {activeContainer ? (
                <p className="text-muted-foreground">
                  File explorer for {activeContainer.name} - Coming soon!
                </p>
              ) : (
                <p className="text-muted-foreground">
                  Select a container to view its files
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="terminal" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Terminal className="h-5 w-5" />
                Terminal
              </CardTitle>
            </CardHeader>
            <CardContent>
              {activeContainer ? (
                <div className="space-y-4">
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={() => handleRunCommand('npm install')}
                    >
                      npm install
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleRunCommand('npm run dev')}
                    >
                      npm run dev
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleRunCommand('npm run build')}
                    >
                      npm run build
                    </Button>
                  </div>
                  
                  <ScrollArea className="h-64 w-full border rounded-md p-4 bg-black text-green-400 font-mono text-sm">
                    {containerProcesses.length === 0 ? (
                      <p>No processes running...</p>
                    ) : (
                      containerProcesses.map((process) => (
                        <div key={process.id} className="mb-2">
                          <p className="text-blue-400">
                            $ {process.command} {process.args.join(' ')}
                          </p>
                          {processOutput[process.id]?.map((output, index) => (
                            <p key={index} className="text-green-400">
                              {output.data}
                            </p>
                          ))}
                        </div>
                      ))
                    )}
                  </ScrollArea>
                </div>
              ) : (
                <p className="text-muted-foreground">
                  Select a container to use the terminal
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
