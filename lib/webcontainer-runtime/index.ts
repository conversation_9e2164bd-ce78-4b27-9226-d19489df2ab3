/**
 * WebContainer Runtime Library
 * 
 * Comprehensive WebContainer-based execution for Node.js applications
 * 
 * @example
 * ```typescript
 * import { useWebContainer, WebContainerManager } from '@/lib/webcontainer-runtime';
 * 
 * // Using React hooks
 * const { container, createContainer, executeCommand } = useWebContainer();
 * 
 * // Using manager directly
 * const manager = new WebContainerManager();
 * await manager.initialize();
 * const containerId = await manager.createContainer(config, files);
 * ```
 */

// Core exports
export { WebContainerManager } from './core/webcontainer-manager';
export { FilesystemManager } from './core/filesystem-manager';
export { ProcessManager } from './core/process-manager';

// API exports
export { WebContainerClient } from './api/webcontainer-client';

// Hook exports
export {
  useWebContainer,
  useWebContainerManager,
  useWebContainerFiles
} from './hooks/use-webcontainer';

// Type exports
export type {
  // Core types
  WebContainerInstance,
  WebContainerConfig,
  WebContainerStatus,
  WebContainerError,
  WebContainerEvent,
  WebContainerEventType,
  WebContainerErrorCode,

  // Filesystem types
  FileEntry,
  FileSystemOperation,
  FileSyncOptions,

  // Process types
  ProcessInfo,
  ProcessOptions,
  ProcessOutput,
  ProcessStatus,

  // API types
  WebContainerAPIResponse,
  CreateContainerRequest,
  ExecuteCommandRequest,
  FileOperationRequest,

  // Store types
  WebContainerState,
  WebContainerSettings,

  // Hook types
  UseWebContainerOptions,
  UseWebContainerReturn,

  // Template types
  WebContainerTemplate,

  // Integration types
  WorkspaceIntegration
} from './types';

// Import types and classes for internal use
import type {
  WebContainerConfig,
  WebContainerSettings,
  WebContainerError,
  WebContainerTemplate
} from './types';
import { WebContainerManager } from './core/webcontainer-manager';
import { WebContainerClient } from './api/webcontainer-client';

// Utility exports
export { createWebContainerTemplate, getDefaultTemplates } from './utils/webcontainer-helpers';

// Constants
export const WEBCONTAINER_RUNTIME_VERSION = '1.0.0';

/**
 * Default WebContainer configuration
 */
export const DEFAULT_WEBCONTAINER_CONFIG: Partial<WebContainerConfig> = {
  environment: {
    NODE_ENV: 'development'
  },
  workingDirectory: '/',
  autoStart: false,
  persistent: true,
  resources: {
    memory: 512, // MB
    cpu: 1
  }
};

/**
 * Default WebContainer settings
 */
export const DEFAULT_WEBCONTAINER_SETTINGS: WebContainerSettings = {
  autoSave: true,
  syncInterval: 5000, // 5 seconds
  maxOutputLines: 1000,
  defaultShell: 'bash',
  defaultWorkingDirectory: '/',
  environmentVariables: {
    NODE_ENV: 'development',
    PATH: '/usr/local/bin:/usr/bin:/bin'
  },
  fileWatchPatterns: ['**/*'],
  excludePatterns: [
    'node_modules/**',
    '.git/**',
    'dist/**',
    'build/**',
    '*.log'
  ]
};

/**
 * Initialize WebContainer runtime with default settings
 */
export async function initializeWebContainerRuntime(
  _config: Partial<WebContainerConfig> = {}
): Promise<WebContainerManager> {
  const manager = new WebContainerManager();
  await manager.initialize();
  return manager;
}

/**
 * Create a WebContainer client with authentication
 */
export function createWebContainerClient(
  baseUrl?: string,
  authHeaders?: Record<string, string>
): WebContainerClient {
  const client = new WebContainerClient(baseUrl);
  if (authHeaders) {
    client.setAuthHeaders(authHeaders);
  }
  return client;
}

/**
 * Error handling utilities
 */
export class WebContainerRuntimeError extends Error {
  public readonly code: string;
  public readonly containerId?: string;
  public readonly processId?: string;
  public readonly timestamp: Date;

  constructor(
    code: string,
    message: string,
    containerId?: string,
    processId?: string
  ) {
    super(message);
    this.name = 'WebContainerRuntimeError';
    this.code = code;
    this.containerId = containerId;
    this.processId = processId;
    this.timestamp = new Date();
  }

  static fromWebContainerError(error: WebContainerError): WebContainerRuntimeError {
    return new WebContainerRuntimeError(
      error.code,
      error.message,
      error.containerId,
      error.processId
    );
  }
}

/**
 * Validation utilities
 */
export function validateWebContainerConfig(config: WebContainerConfig): boolean {
  if (!config.name || typeof config.name !== 'string') {
    throw new WebContainerRuntimeError(
      'INVALID_CONFIG',
      'Container name is required and must be a string'
    );
  }

  if (config.resources) {
    if (config.resources.memory && config.resources.memory < 128) {
      throw new WebContainerRuntimeError(
        'INVALID_CONFIG',
        'Memory allocation must be at least 128MB'
      );
    }

    if (config.resources.cpu && config.resources.cpu < 0.1) {
      throw new WebContainerRuntimeError(
        'INVALID_CONFIG',
        'CPU allocation must be at least 0.1'
      );
    }
  }

  return true;
}

/**
 * Template utilities
 */
export function createReactTemplate(): WebContainerTemplate {
  return {
    id: 'react-typescript',
    name: 'React TypeScript',
    description: 'React application with TypeScript and Vite',
    category: 'frontend',
    files: {
      'package.json': {
        file: {
          contents: JSON.stringify({
            name: 'react-app',
            version: '1.0.0',
            type: 'module',
            scripts: {
              dev: 'vite',
              build: 'vite build',
              preview: 'vite preview'
            },
            dependencies: {
              react: '^18.2.0',
              'react-dom': '^18.2.0'
            },
            devDependencies: {
              '@types/react': '^18.2.0',
              '@types/react-dom': '^18.2.0',
              '@vitejs/plugin-react': '^4.0.0',
              typescript: '^5.0.0',
              vite: '^4.0.0'
            }
          }, null, 2)
        }
      },
      'index.html': {
        file: {
          contents: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>React App</title>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>
</html>`
        }
      },
      'src': {
        directory: {
          'main.tsx': {
            file: {
              contents: `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)`
            }
          },
          'App.tsx': {
            file: {
              contents: `import React from 'react'

function App() {
  return (
    <div>
      <h1>Hello WebContainer!</h1>
      <p>React app running in WebContainer</p>
    </div>
  )
}

export default App`
            }
          }
        }
      },
      'vite.config.ts': {
        file: {
          contents: `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    host: true,
    port: 3000
  }
})`
        }
      },
      'tsconfig.json': {
        file: {
          contents: JSON.stringify({
            compilerOptions: {
              target: 'ES2020',
              useDefineForClassFields: true,
              lib: ['ES2020', 'DOM', 'DOM.Iterable'],
              module: 'ESNext',
              skipLibCheck: true,
              moduleResolution: 'bundler',
              allowImportingTsExtensions: true,
              resolveJsonModule: true,
              isolatedModules: true,
              noEmit: true,
              jsx: 'react-jsx',
              strict: true,
              noUnusedLocals: true,
              noUnusedParameters: true,
              noFallthroughCasesInSwitch: true
            },
            include: ['src'],
            references: [{ path: './tsconfig.node.json' }]
          }, null, 2)
        }
      }
    },
    config: {
      name: 'react-app',
      environment: {
        NODE_ENV: 'development'
      }
    },
    dependencies: ['react', 'react-dom', 'vite'],
    scripts: {
      dev: 'npm run dev',
      build: 'npm run build'
    },
    tags: ['react', 'typescript', 'vite', 'frontend']
  };
}

export function createNodeTemplate(): WebContainerTemplate {
  return {
    id: 'node-express',
    name: 'Node.js Express',
    description: 'Node.js application with Express server',
    category: 'backend',
    files: {
      'package.json': {
        file: {
          contents: JSON.stringify({
            name: 'node-app',
            version: '1.0.0',
            type: 'module',
            scripts: {
              start: 'node server.js',
              dev: 'node --watch server.js'
            },
            dependencies: {
              express: '^4.18.0'
            }
          }, null, 2)
        }
      },
      'server.js': {
        file: {
          contents: `import express from 'express';

const app = express();
const port = 3000;

app.use(express.json());

app.get('/', (req, res) => {
  res.json({ message: 'Hello from WebContainer!' });
});

app.listen(port, () => {
  console.log(\`Server running at http://localhost:\${port}\`);
});`
        }
      }
    },
    config: {
      name: 'node-app',
      environment: {
        NODE_ENV: 'development'
      }
    },
    dependencies: ['express'],
    scripts: {
      start: 'npm start',
      dev: 'npm run dev'
    },
    tags: ['node', 'express', 'backend', 'api']
  };
}
