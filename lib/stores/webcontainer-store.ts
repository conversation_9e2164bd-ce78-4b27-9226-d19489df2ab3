/**
 * WebContainer Store
 * 
 * Zustand store for WebContainer state management
 */

import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { useEffect, useMemo } from 'react';
import {
  WebContainerInstance,
  WebContainerConfig,
  WebContainerError,
  WebContainerSettings,
  ProcessInfo,
  FileEntry,
  ProcessOutput,
  WebContainerTemplate,
  DEFAULT_WEBCONTAINER_SETTINGS
} from '@/lib/webcontainer-runtime/types';
import { WebContainerClient } from '@/lib/webcontainer-runtime/api/webcontainer-client';
import { getDefaultTemplates } from '@/lib/webcontainer-runtime/utils/webcontainer-helpers';

// Store state interface
interface WebContainerState {
  // Core state
  containers: WebContainerInstance[];
  activeContainerId: string | null;
  isLoading: boolean;
  error: WebContainerError | null;
  
  // Runtime management
  client: WebContainerClient | null;
  
  // File system state
  fileSystem: Record<string, FileEntry[]>; // containerId -> files
  activeFile: { containerId: string; path: string; content: string } | null;
  syncStatus: Record<string, 'syncing' | 'synced' | 'error'>; // containerId -> status
  
  // Process state
  processes: Record<string, ProcessInfo[]>; // containerId -> processes
  activeProcess: { containerId: string; processId: string } | null;
  processOutput: Record<string, ProcessOutput[]>; // processId -> output
  
  // Templates
  availableTemplates: WebContainerTemplate[];
  
  // Settings
  globalSettings: WebContainerSettings;
  containerSettings: Record<string, WebContainerSettings>; // containerId -> settings
}

// Store actions interface
interface WebContainerActions {
  // Runtime management
  initializeClient: (baseUrl?: string) => void;
  
  // Container management
  createContainer: (config: WebContainerConfig, files?: any) => Promise<string>;
  getContainer: (containerId: string) => Promise<WebContainerInstance | null>;
  listContainers: () => Promise<void>;
  startContainer: (containerId: string) => Promise<void>;
  stopContainer: (containerId: string) => Promise<void>;
  destroyContainer: (containerId: string) => Promise<void>;
  setActiveContainer: (containerId: string | null) => void;
  
  // File operations
  readFile: (containerId: string, path: string) => Promise<string>;
  writeFile: (containerId: string, path: string, content: string) => Promise<void>;
  deleteFile: (containerId: string, path: string) => Promise<void>;
  listFiles: (containerId: string, path?: string) => Promise<void>;
  setActiveFile: (containerId: string, path: string, content: string) => void;
  
  // Process operations
  executeCommand: (containerId: string, command: string, args?: string[], options?: any) => Promise<ProcessInfo>;
  killProcess: (containerId: string, processId: string) => Promise<void>;
  getProcessOutput: (processId: string) => ProcessOutput[];
  addProcessOutput: (processId: string, output: ProcessOutput) => void;
  setActiveProcess: (containerId: string, processId: string) => void;
  
  // Development operations
  startDevServer: (containerId: string, command?: string) => Promise<ProcessInfo>;
  installPackages: (containerId: string, packages: string[], options?: any) => Promise<ProcessInfo>;
  runScript: (containerId: string, scriptName: string, packageManager?: string) => Promise<ProcessInfo>;
  
  // Settings
  updateGlobalSettings: (settings: Partial<WebContainerSettings>) => void;
  updateContainerSettings: (containerId: string, settings: Partial<WebContainerSettings>) => void;
  
  // Templates
  getTemplates: () => WebContainerTemplate[];
  createFromTemplate: (templateId: string, config: Partial<WebContainerConfig>) => Promise<string>;
  
  // Cleanup
  cleanup: () => void;
}

type WebContainerStore = WebContainerState & WebContainerActions;

// Create the WebContainer store
export const useWebContainerStore = create<WebContainerStore>()(
  devtools(
    subscribeWithSelector(
      immer((set, get) => ({
        // Initial state
        containers: [],
        activeContainerId: null,
        isLoading: false,
        error: null,
        
        client: null,
        
        fileSystem: {},
        activeFile: null,
        syncStatus: {},
        
        processes: {},
        activeProcess: null,
        processOutput: {},
        
        availableTemplates: getDefaultTemplates(),
        
        globalSettings: DEFAULT_WEBCONTAINER_SETTINGS,
        containerSettings: {},

        // Initialize client
        initializeClient: (baseUrl = '/api/webcontainer-api') => {
          set((state) => {
            state.client = new WebContainerClient(baseUrl);
          });
        },

        // Container management
        createContainer: async (config, files) => {
          const state = get();
          if (!state.client) {
            throw new Error('WebContainer client not initialized');
          }

          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            const containerId = await state.client.createContainer(config, files);
            
            // Refresh container list
            await get().listContainers();
            
            set((state) => {
              state.activeContainerId = containerId;
              state.isLoading = false;
            });

            return containerId;
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
              state.isLoading = false;
            });
            throw error;
          }
        },

        getContainer: async (containerId) => {
          const state = get();
          if (!state.client) return null;

          try {
            return await state.client.getContainer(containerId);
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
            });
            return null;
          }
        },

        listContainers: async () => {
          const state = get();
          if (!state.client) return;

          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            const containers = await state.client.listContainers();
            
            set((state) => {
              state.containers = containers;
              state.isLoading = false;
            });
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
              state.isLoading = false;
            });
          }
        },

        startContainer: async (containerId) => {
          const state = get();
          if (!state.client) return;

          try {
            await state.client.startContainer(containerId);
            await get().listContainers(); // Refresh
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
            });
            throw error;
          }
        },

        stopContainer: async (containerId) => {
          const state = get();
          if (!state.client) return;

          try {
            await state.client.stopContainer(containerId);
            await get().listContainers(); // Refresh
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
            });
            throw error;
          }
        },

        destroyContainer: async (containerId) => {
          const state = get();
          if (!state.client) return;

          try {
            await state.client.destroyContainer(containerId);
            
            set((state) => {
              // Remove from state
              state.containers = state.containers.filter(c => c.id !== containerId);
              delete state.fileSystem[containerId];
              delete state.processes[containerId];
              delete state.containerSettings[containerId];
              delete state.syncStatus[containerId];
              
              if (state.activeContainerId === containerId) {
                state.activeContainerId = null;
                state.activeFile = null;
                state.activeProcess = null;
              }
            });
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
            });
            throw error;
          }
        },

        setActiveContainer: (containerId) => {
          set((state) => {
            state.activeContainerId = containerId;
            state.activeFile = null;
            state.activeProcess = null;
          });
        },

        // File operations
        readFile: async (containerId, path) => {
          const state = get();
          if (!state.client) {
            throw new Error('WebContainer client not initialized');
          }

          try {
            return await state.client.readFile(containerId, path);
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
            });
            throw error;
          }
        },

        writeFile: async (containerId, path, content) => {
          const state = get();
          if (!state.client) return;

          try {
            await state.client.writeFile(containerId, path, content);
            
            // Update active file if it's the same
            set((state) => {
              if (state.activeFile?.containerId === containerId && state.activeFile?.path === path) {
                state.activeFile.content = content;
              }
            });
            
            // Refresh file list
            await get().listFiles(containerId);
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
            });
            throw error;
          }
        },

        deleteFile: async (containerId, path) => {
          const state = get();
          if (!state.client) return;

          try {
            await state.client.deleteFile(containerId, path);
            
            // Clear active file if it's the deleted one
            set((state) => {
              if (state.activeFile?.containerId === containerId && state.activeFile?.path === path) {
                state.activeFile = null;
              }
            });
            
            // Refresh file list
            await get().listFiles(containerId);
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
            });
            throw error;
          }
        },

        listFiles: async (containerId, path = '/') => {
          const state = get();
          if (!state.client) return;

          try {
            const files = await state.client.listFiles(containerId, path);
            
            set((state) => {
              state.fileSystem[containerId] = files;
            });
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
            });
          }
        },

        setActiveFile: (containerId, path, content) => {
          set((state) => {
            state.activeFile = { containerId, path, content };
          });
        },

        // Process operations
        executeCommand: async (containerId, command, args = [], options = {}) => {
          const state = get();
          if (!state.client) {
            throw new Error('WebContainer client not initialized');
          }

          try {
            const processInfo = await state.client.executeCommand(containerId, command, args, options);
            
            set((state) => {
              if (!state.processes[containerId]) {
                state.processes[containerId] = [];
              }
              state.processes[containerId].push(processInfo);
              state.processOutput[processInfo.id] = [];
            });

            return processInfo;
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
            });
            throw error;
          }
        },

        killProcess: async (containerId, processId) => {
          const state = get();
          if (!state.client) return;

          try {
            await state.client.killProcess(containerId, processId);
            
            set((state) => {
              const processes = state.processes[containerId];
              if (processes) {
                const process = processes.find(p => p.id === processId);
                if (process) {
                  process.status = 'killed';
                  process.endTime = new Date();
                }
              }
            });
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
            });
            throw error;
          }
        },

        getProcessOutput: (processId) => {
          const state = get();
          return state.processOutput[processId] || [];
        },

        addProcessOutput: (processId, output) => {
          set((state) => {
            if (!state.processOutput[processId]) {
              state.processOutput[processId] = [];
            }
            state.processOutput[processId].push(output);
            
            // Limit output buffer size
            const maxLines = state.globalSettings.maxOutputLines;
            if (state.processOutput[processId].length > maxLines) {
              state.processOutput[processId] = state.processOutput[processId].slice(-maxLines);
            }
          });
        },

        setActiveProcess: (containerId, processId) => {
          set((state) => {
            state.activeProcess = { containerId, processId };
          });
        },

        // Development operations
        startDevServer: async (containerId, command = 'npm run dev') => {
          const state = get();
          if (!state.client) {
            throw new Error('WebContainer client not initialized');
          }

          try {
            return await state.client.startDevServer(containerId, command);
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
            });
            throw error;
          }
        },

        installPackages: async (containerId, packages, options = {}) => {
          const state = get();
          if (!state.client) {
            throw new Error('WebContainer client not initialized');
          }

          try {
            return await state.client.installPackages(containerId, packages, options);
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
            });
            throw error;
          }
        },

        runScript: async (containerId, scriptName, packageManager = 'npm') => {
          const state = get();
          if (!state.client) {
            throw new Error('WebContainer client not initialized');
          }

          try {
            return await state.client.runScript(containerId, scriptName, packageManager);
          } catch (error) {
            set((state) => {
              state.error = error as WebContainerError;
            });
            throw error;
          }
        },

        // Settings
        updateGlobalSettings: (settings) => {
          set((state) => {
            Object.assign(state.globalSettings, settings);
          });
        },

        updateContainerSettings: (containerId, settings) => {
          set((state) => {
            if (!state.containerSettings[containerId]) {
              state.containerSettings[containerId] = { ...DEFAULT_WEBCONTAINER_SETTINGS };
            }
            Object.assign(state.containerSettings[containerId], settings);
          });
        },

        // Templates
        getTemplates: () => {
          return get().availableTemplates;
        },

        createFromTemplate: async (templateId, config) => {
          const state = get();
          const template = state.availableTemplates.find(t => t.id === templateId);
          
          if (!template) {
            throw new Error(`Template ${templateId} not found`);
          }

          const containerConfig = {
            ...template.config,
            ...config,
            name: config.name || template.name
          };

          return await get().createContainer(containerConfig, template.files);
        },

        // Cleanup
        cleanup: () => {
          set((state) => {
            state.containers = [];
            state.activeContainerId = null;
            state.fileSystem = {};
            state.activeFile = null;
            state.processes = {};
            state.activeProcess = null;
            state.processOutput = {};
            state.syncStatus = {};
            state.error = null;
            state.isLoading = false;
            state.client = null;
          });
        },
      }))
    ),
    {
      name: 'webcontainer-store',
      partialize: (state: WebContainerState) => ({
        globalSettings: state.globalSettings,
        containerSettings: state.containerSettings,
      }),
    }
  )
);

// Integration hook for workspace
export function useWebContainerIntegration(projectId?: string) {
  const {
    containers,
    activeContainerId,
    isLoading,
    error,
    initializeClient,
    listContainers,
    setActiveContainer
  } = useWebContainerStore();

  // Initialize client on mount
  useEffect(() => {
    initializeClient();
    listContainers();
  }, [initializeClient, listContainers]);

  // Find active container
  const activeContainer = useMemo(() => {
    return containers.find(c => c.id === activeContainerId) || null;
  }, [containers, activeContainerId]);

  // Auto-select container for project
  useEffect(() => {
    if (projectId && containers.length > 0 && !activeContainerId) {
      const projectContainer = containers.find(c => 
        c.config.projectId === projectId || c.metadata?.projectId === projectId
      );
      if (projectContainer) {
        setActiveContainer(projectContainer.id);
      }
    }
  }, [projectId, containers, activeContainerId, setActiveContainer]);

  return {
    containers,
    activeContainer,
    isLoading,
    error,
    setActiveContainer
  };
}
