/**
 * Linux System Dashboard Component
 * 
 * Comprehensive dashboard for Linux system management via WebSocket API
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  HardDrive, 
  Memory, 
  Network, 
  Package, 
  Terminal, 
  Settings,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useLinuxSystem } from '@/lib/linux-system-api/hooks/use-linux-system';

export function LinuxSystemDashboard() {
  const {
    systemInfo,
    distributionInfo,
    isLoading,
    error,
    isConnected,
    getSystemInfo,
    executeCommand,
    getInstalledPackages,
    searchPackages,
    installPackage,
    removePackage,
    refresh
  } = useLinuxSystem({ 
    autoInitialize: true, 
    enableWebSocket: true 
  });

  const [commandInput, setCommandInput] = useState('');
  const [commandOutput, setCommandOutput] = useState('');
  const [commandLoading, setCommandLoading] = useState(false);
  const [packageQuery, setPackageQuery] = useState('');
  const [packageResults, setPackageResults] = useState<any[]>([]);
  const [installedPackages, setInstalledPackages] = useState<any[]>([]);

  // Load installed packages on mount
  useEffect(() => {
    if (isConnected) {
      loadInstalledPackages();
    }
  }, [isConnected]);

  const loadInstalledPackages = async () => {
    try {
      const packages = await getInstalledPackages();
      setInstalledPackages(packages.slice(0, 50)); // Limit to first 50 for performance
    } catch (err) {
      console.error('Failed to load installed packages:', err);
    }
  };

  const handleCommandExecute = async () => {
    if (!commandInput.trim()) return;

    setCommandLoading(true);
    try {
      const result = await executeCommand(commandInput);
      setCommandOutput(prev => 
        `${prev}\n$ ${commandInput}\n${result.stdout}${result.stderr ? `\nERROR: ${result.stderr}` : ''}\n`
      );
      setCommandInput('');
    } catch (err) {
      setCommandOutput(prev => 
        `${prev}\n$ ${commandInput}\nERROR: ${err instanceof Error ? err.message : 'Unknown error'}\n`
      );
    } finally {
      setCommandLoading(false);
    }
  };

  const handlePackageSearch = async () => {
    if (!packageQuery.trim()) return;

    try {
      const results = await searchPackages(packageQuery);
      setPackageResults(results.slice(0, 20)); // Limit results
    } catch (err) {
      console.error('Package search failed:', err);
    }
  };

  const handlePackageInstall = async (packageName: string) => {
    try {
      await installPackage(packageName, { noConfirm: true });
      await loadInstalledPackages(); // Refresh installed packages
    } catch (err) {
      console.error('Package installation failed:', err);
    }
  };

  const handlePackageRemove = async (packageName: string) => {
    try {
      await removePackage(packageName, { noConfirm: true });
      await loadInstalledPackages(); // Refresh installed packages
    } catch (err) {
      console.error('Package removal failed:', err);
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  if (error) {
    return (
      <Alert className="m-4">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to connect to Linux System API: {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Linux System Dashboard</h1>
          <p className="text-muted-foreground">
            {distributionInfo ? 
              `${distributionInfo.name} ${distributionInfo.version} - ${distributionInfo.packageManager}/${distributionInfo.serviceManager}` :
              'Loading system information...'
            }
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={isConnected ? "default" : "destructive"}>
            {isConnected ? "Connected" : "Disconnected"}
          </Badge>
          <Button onClick={refresh} disabled={isLoading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* System Overview Cards */}
      {systemInfo && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {systemInfo.loadAverage?.[0]?.toFixed(2) || 'N/A'}
              </div>
              <p className="text-xs text-muted-foreground">
                Load Average (1m)
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Memory</CardTitle>
              <Memory className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {systemInfo.memory ? 
                  `${Math.round((systemInfo.memory.used / systemInfo.memory.total) * 100)}%` : 
                  'N/A'
                }
              </div>
              <p className="text-xs text-muted-foreground">
                {systemInfo.memory ? 
                  `${formatBytes(systemInfo.memory.used)} / ${formatBytes(systemInfo.memory.total)}` :
                  'Loading...'
                }
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Disk Usage</CardTitle>
              <HardDrive className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {systemInfo.disk?.[0]?.usePercent || 'N/A'}%
              </div>
              <p className="text-xs text-muted-foreground">
                {systemInfo.disk?.[0] ? 
                  `${formatBytes(systemInfo.disk[0].used)} / ${formatBytes(systemInfo.disk[0].size)}` :
                  'No disk info'
                }
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Uptime</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {systemInfo.uptime ? formatUptime(systemInfo.uptime) : 'N/A'}
              </div>
              <p className="text-xs text-muted-foreground">
                {systemInfo.hostname || 'Unknown host'}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="terminal" className="space-y-4">
        <TabsList>
          <TabsTrigger value="terminal">
            <Terminal className="h-4 w-4 mr-2" />
            Terminal
          </TabsTrigger>
          <TabsTrigger value="packages">
            <Package className="h-4 w-4 mr-2" />
            Packages
          </TabsTrigger>
          <TabsTrigger value="system">
            <Activity className="h-4 w-4 mr-2" />
            System Info
          </TabsTrigger>
        </TabsList>

        {/* Terminal Tab */}
        <TabsContent value="terminal">
          <Card>
            <CardHeader>
              <CardTitle>Terminal</CardTitle>
              <CardDescription>
                Execute commands on the Linux system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-2">
                <Input
                  placeholder="Enter command..."
                  value={commandInput}
                  onChange={(e) => setCommandInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleCommandExecute()}
                  className="flex-1"
                />
                <Button 
                  onClick={handleCommandExecute} 
                  disabled={commandLoading || !commandInput.trim()}
                >
                  {commandLoading ? 'Running...' : 'Execute'}
                </Button>
              </div>
              <ScrollArea className="h-64 w-full border rounded-md p-4">
                <pre className="text-sm font-mono whitespace-pre-wrap">
                  {commandOutput || 'No commands executed yet...'}
                </pre>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Packages Tab */}
        <TabsContent value="packages">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Package Search */}
            <Card>
              <CardHeader>
                <CardTitle>Search Packages</CardTitle>
                <CardDescription>
                  Search for available packages
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex space-x-2">
                  <Input
                    placeholder="Search packages..."
                    value={packageQuery}
                    onChange={(e) => setPackageQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handlePackageSearch()}
                  />
                  <Button onClick={handlePackageSearch}>Search</Button>
                </div>
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {packageResults.map((pkg, index) => (
                      <div key={index} className="flex items-center justify-between p-2 border rounded">
                        <div>
                          <div className="font-medium">{pkg.name}</div>
                          <div className="text-sm text-muted-foreground">{pkg.description}</div>
                        </div>
                        <Button 
                          size="sm" 
                          onClick={() => handlePackageInstall(pkg.name)}
                        >
                          Install
                        </Button>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Installed Packages */}
            <Card>
              <CardHeader>
                <CardTitle>Installed Packages</CardTitle>
                <CardDescription>
                  Currently installed packages (showing first 50)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {installedPackages.map((pkg, index) => (
                      <div key={index} className="flex items-center justify-between p-2 border rounded">
                        <div>
                          <div className="font-medium">{pkg.name}</div>
                          <div className="text-sm text-muted-foreground">{pkg.version}</div>
                        </div>
                        <Button 
                          size="sm" 
                          variant="destructive"
                          onClick={() => handlePackageRemove(pkg.name)}
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* System Info Tab */}
        <TabsContent value="system">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {systemInfo && (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle>System Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="font-medium">Hostname:</div>
                      <div>{systemInfo.hostname}</div>
                      <div className="font-medium">Distribution:</div>
                      <div>{systemInfo.distribution} {systemInfo.version}</div>
                      <div className="font-medium">Architecture:</div>
                      <div>{systemInfo.architecture}</div>
                      <div className="font-medium">Kernel:</div>
                      <div>{systemInfo.kernel}</div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Resource Usage</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {systemInfo.memory && (
                      <div>
                        <div className="flex justify-between text-sm">
                          <span>Memory</span>
                          <span>{Math.round((systemInfo.memory.used / systemInfo.memory.total) * 100)}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${(systemInfo.memory.used / systemInfo.memory.total) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                    
                    {systemInfo.disk?.[0] && (
                      <div>
                        <div className="flex justify-between text-sm">
                          <span>Disk ({systemInfo.disk[0].mountpoint})</span>
                          <span>{systemInfo.disk[0].usePercent}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-600 h-2 rounded-full" 
                            style={{ width: `${systemInfo.disk[0].usePercent}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
