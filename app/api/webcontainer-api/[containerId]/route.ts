/**
 * WebContainer Individual Container API Routes
 * 
 * API endpoints for individual container operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { WebContainerManager } from '@/lib/webcontainer-runtime/core/webcontainer-manager';
import { FilesystemManager } from '@/lib/webcontainer-runtime/core/filesystem-manager';
import { ProcessManager } from '@/lib/webcontainer-runtime/core/process-manager';
import {
  WebContainerAPIResponse,
  WebContainerError
} from '@/lib/webcontainer-runtime/types';

// Global manager instances (in production, these would be properly managed)
let webContainerManager: WebContainerManager | null = null;
let filesystemManager: FilesystemManager | null = null;
let processManager: ProcessManager | null = null;

// Initialize managers
async function initializeManagers() {
  if (!webContainerManager) {
    webContainerManager = new WebContainerManager();
    await webContainerManager.initialize();
    
    filesystemManager = new FilesystemManager(webContainerManager);
    processManager = new ProcessManager(webContainerManager);
  }
}

/**
 * GET /api/webcontainer-api/[containerId]
 * Get container information
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { containerId: string } }
): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    if (!webContainerManager) {
      throw new Error('WebContainer manager not initialized');
    }

    const { containerId } = params;
    const container = webContainerManager.getContainer(containerId);

    if (!container) {
      const errorResponse: WebContainerAPIResponse = {
        success: false,
        error: `Container ${containerId} not found`,
        timestamp: new Date()
      };
      return NextResponse.json(errorResponse, { status: 404 });
    }

    const response: WebContainerAPIResponse = {
      success: true,
      data: container,
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error getting container:', error);
    return createErrorResponse(error);
  }
}

/**
 * PUT /api/webcontainer-api/[containerId]
 * Update container configuration
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { containerId: string } }
): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    if (!webContainerManager) {
      throw new Error('WebContainer manager not initialized');
    }

    const { containerId } = params;
    const container = webContainerManager.getContainer(containerId);

    if (!container) {
      const errorResponse: WebContainerAPIResponse = {
        success: false,
        error: `Container ${containerId} not found`,
        timestamp: new Date()
      };
      return NextResponse.json(errorResponse, { status: 404 });
    }

    const body = await request.json();
    
    // Update container configuration
    if (body.config) {
      Object.assign(container.config, body.config);
    }

    // Update metadata
    if (body.metadata) {
      Object.assign(container.metadata, body.metadata);
    }

    container.lastActivity = new Date();

    const response: WebContainerAPIResponse = {
      success: true,
      data: container,
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error updating container:', error);
    return createErrorResponse(error);
  }
}

/**
 * DELETE /api/webcontainer-api/[containerId]
 * Destroy a container
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { containerId: string } }
): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    if (!webContainerManager) {
      throw new Error('WebContainer manager not initialized');
    }

    const { containerId } = params;
    
    await webContainerManager.destroyContainer(containerId);

    const response: WebContainerAPIResponse = {
      success: true,
      data: { message: `Container ${containerId} destroyed` },
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error destroying container:', error);
    return createErrorResponse(error);
  }
}

/**
 * POST /api/webcontainer-api/[containerId]/start
 * Start a container
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { containerId: string } }
): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    if (!webContainerManager) {
      throw new Error('WebContainer manager not initialized');
    }

    const { containerId } = params;
    const url = new URL(request.url);
    const action = url.pathname.split('/').pop();

    switch (action) {
      case 'start':
        await webContainerManager.startContainer(containerId);
        break;
      case 'stop':
        await webContainerManager.stopContainer(containerId);
        break;
      default:
        throw new Error(`Unknown action: ${action}`);
    }

    const container = webContainerManager.getContainer(containerId);

    const response: WebContainerAPIResponse = {
      success: true,
      data: container,
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error performing container action:', error);
    return createErrorResponse(error);
  }
}

// Error handling utility
function createErrorResponse(
  error: unknown,
  status: number = 500
): NextResponse {
  let errorMessage = 'Unknown error';
  let errorCode = 'UNKNOWN_ERROR';

  if (error instanceof Error) {
    errorMessage = error.message;
  }

  if (typeof error === 'object' && error !== null && 'code' in error) {
    errorCode = (error as WebContainerError).code;
  }

  const errorResponse: WebContainerAPIResponse = {
    success: false,
    error: errorMessage,
    timestamp: new Date()
  };

  return NextResponse.json(errorResponse, { status });
}
