/**
 * WebContainer Filesystem API Routes
 * 
 * API endpoints for filesystem operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { WebContainerManager } from '@/lib/webcontainer-runtime/core/webcontainer-manager';
import { FilesystemManager } from '@/lib/webcontainer-runtime/core/filesystem-manager';
import {
  WebContainerAPIResponse,
  FileOperationRequest,
  WebContainerError
} from '@/lib/webcontainer-runtime/types';

// Global manager instances
let webContainerManager: WebContainerManager | null = null;
let filesystemManager: FilesystemManager | null = null;

// Initialize managers
async function initializeManagers() {
  if (!webContainerManager) {
    webContainerManager = new WebContainerManager();
    await webContainerManager.initialize();
    
    filesystemManager = new FilesystemManager(webContainerManager);
  }
}

/**
 * GET /api/webcontainer-api/[containerId]/filesystem
 * List files or read file content
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { containerId: string } }
): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    if (!webContainerManager || !filesystemManager) {
      throw new Error('Managers not initialized');
    }

    const { containerId } = params;
    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path') || '/';
    const action = searchParams.get('action') || 'list';

    // Verify container exists
    const container = webContainerManager.getContainer(containerId);
    if (!container) {
      const errorResponse: WebContainerAPIResponse = {
        success: false,
        error: `Container ${containerId} not found`,
        timestamp: new Date()
      };
      return NextResponse.json(errorResponse, { status: 404 });
    }

    let data: any;

    switch (action) {
      case 'list':
        data = await filesystemManager.getFileTree(containerId, path);
        break;
      case 'read':
        const content = await webContainerManager.readFile(containerId, path);
        data = { content, path };
        break;
      case 'export':
        data = await filesystemManager.exportFileSystem(
          container,
          path === '/' ? '/' : path
        );
        break;
      default:
        throw new Error(`Unknown action: ${action}`);
    }

    const response: WebContainerAPIResponse = {
      success: true,
      data,
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in filesystem GET:', error);
    return createErrorResponse(error);
  }
}

/**
 * POST /api/webcontainer-api/[containerId]/filesystem
 * Perform filesystem operations
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { containerId: string } }
): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    if (!webContainerManager || !filesystemManager) {
      throw new Error('Managers not initialized');
    }

    const { containerId } = params;
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'operation';

    // Verify container exists
    const container = webContainerManager.getContainer(containerId);
    if (!container) {
      const errorResponse: WebContainerAPIResponse = {
        success: false,
        error: `Container ${containerId} not found`,
        timestamp: new Date()
      };
      return NextResponse.json(errorResponse, { status: 404 });
    }

    const body = await request.json();

    let data: any;

    switch (action) {
      case 'operation':
        if (!body.operation) {
          throw new Error('Operation is required');
        }
        await filesystemManager.performOperation(containerId, body.operation);
        data = { message: 'Operation completed successfully' };
        break;

      case 'write':
        if (!body.path || body.content === undefined) {
          throw new Error('Path and content are required');
        }
        await webContainerManager.writeFile(containerId, body.path, body.content);
        data = { message: 'File written successfully', path: body.path };
        break;

      case 'mount':
        if (!body.files) {
          throw new Error('Files are required for mount operation');
        }
        await filesystemManager.mountFiles(containerId, body.files, body.options);
        data = { message: 'Files mounted successfully' };
        break;

      case 'sync-to-local':
        if (!body.localPath) {
          throw new Error('Local path is required');
        }
        await filesystemManager.syncToLocal(containerId, body.localPath, body.options);
        data = { message: 'Synced to local successfully' };
        break;

      case 'sync-from-local':
        if (!body.localPath) {
          throw new Error('Local path is required');
        }
        await filesystemManager.syncFromLocal(containerId, body.localPath, body.options);
        data = { message: 'Synced from local successfully' };
        break;

      case 'watch':
        await filesystemManager.startWatching(containerId, body.options);
        data = { message: 'File watching started' };
        break;

      default:
        throw new Error(`Unknown action: ${action}`);
    }

    const response: WebContainerAPIResponse = {
      success: true,
      data,
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in filesystem POST:', error);
    return createErrorResponse(error);
  }
}

/**
 * PUT /api/webcontainer-api/[containerId]/filesystem
 * Update file content
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { containerId: string } }
): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    if (!webContainerManager) {
      throw new Error('WebContainer manager not initialized');
    }

    const { containerId } = params;
    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path');

    if (!path) {
      throw new Error('File path is required');
    }

    // Verify container exists
    const container = webContainerManager.getContainer(containerId);
    if (!container) {
      const errorResponse: WebContainerAPIResponse = {
        success: false,
        error: `Container ${containerId} not found`,
        timestamp: new Date()
      };
      return NextResponse.json(errorResponse, { status: 404 });
    }

    const body = await request.json();
    
    if (body.content === undefined) {
      throw new Error('File content is required');
    }

    await webContainerManager.writeFile(containerId, path, body.content);

    const response: WebContainerAPIResponse = {
      success: true,
      data: { message: 'File updated successfully', path },
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error updating file:', error);
    return createErrorResponse(error);
  }
}

/**
 * DELETE /api/webcontainer-api/[containerId]/filesystem
 * Delete file or directory
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { containerId: string } }
): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    if (!webContainerManager || !filesystemManager) {
      throw new Error('Managers not initialized');
    }

    const { containerId } = params;
    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path');

    if (!path) {
      throw new Error('File path is required');
    }

    // Verify container exists
    const container = webContainerManager.getContainer(containerId);
    if (!container) {
      const errorResponse: WebContainerAPIResponse = {
        success: false,
        error: `Container ${containerId} not found`,
        timestamp: new Date()
      };
      return NextResponse.json(errorResponse, { status: 404 });
    }

    const operation = {
      type: 'delete' as const,
      path,
      timestamp: new Date()
    };

    await filesystemManager.performOperation(containerId, operation);

    const response: WebContainerAPIResponse = {
      success: true,
      data: { message: 'File deleted successfully', path },
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error deleting file:', error);
    return createErrorResponse(error);
  }
}

// Error handling utility
function createErrorResponse(
  error: unknown,
  status: number = 500
): NextResponse {
  let errorMessage = 'Unknown error';
  let errorCode = 'UNKNOWN_ERROR';

  if (error instanceof Error) {
    errorMessage = error.message;
  }

  if (typeof error === 'object' && error !== null && 'code' in error) {
    errorCode = (error as WebContainerError).code;
  }

  const errorResponse: WebContainerAPIResponse = {
    success: false,
    error: errorMessage,
    timestamp: new Date()
  };

  return NextResponse.json(errorResponse, { status });
}
