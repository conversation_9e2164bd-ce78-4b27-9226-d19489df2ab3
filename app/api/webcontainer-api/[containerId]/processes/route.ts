/**
 * WebContainer Processes API Routes
 * 
 * API endpoints for process management
 */

import { NextRequest, NextResponse } from 'next/server';
import { WebContainerManager } from '@/lib/webcontainer-runtime/core/webcontainer-manager';
import { ProcessManager } from '@/lib/webcontainer-runtime/core/process-manager';
import {
  WebContainerAPIResponse,
  ExecuteCommandRequest,
  WebContainerError
} from '@/lib/webcontainer-runtime/types';

// Global manager instances
let webContainerManager: WebContainerManager | null = null;
let processManager: ProcessManager | null = null;

// Initialize managers
async function initializeManagers() {
  if (!webContainerManager) {
    webContainerManager = new WebContainerManager();
    await webContainerManager.initialize();
    
    processManager = new ProcessManager(webContainerManager);
  }
}

/**
 * GET /api/webcontainer-api/[containerId]/processes
 * List processes or get process information
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { containerId: string } }
): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    if (!webContainerManager || !processManager) {
      throw new Error('Managers not initialized');
    }

    const { containerId } = params;
    const { searchParams } = new URL(request.url);
    const processId = searchParams.get('processId');

    // Verify container exists
    const container = webContainerManager.getContainer(containerId);
    if (!container) {
      const errorResponse: WebContainerAPIResponse = {
        success: false,
        error: `Container ${containerId} not found`,
        timestamp: new Date()
      };
      return NextResponse.json(errorResponse, { status: 404 });
    }

    let data: any;

    if (processId) {
      // Get specific process
      const process = processManager.getProcess(processId);
      if (!process) {
        const errorResponse: WebContainerAPIResponse = {
          success: false,
          error: `Process ${processId} not found`,
          timestamp: new Date()
        };
        return NextResponse.json(errorResponse, { status: 404 });
      }
      data = process;
    } else {
      // List all processes for container
      data = processManager.getContainerProcesses(containerId);
    }

    const response: WebContainerAPIResponse = {
      success: true,
      data,
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in processes GET:', error);
    return createErrorResponse(error);
  }
}

/**
 * POST /api/webcontainer-api/[containerId]/processes
 * Execute commands and manage processes
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { containerId: string } }
): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    if (!webContainerManager || !processManager) {
      throw new Error('Managers not initialized');
    }

    const { containerId } = params;
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'execute';

    // Verify container exists
    const container = webContainerManager.getContainer(containerId);
    if (!container) {
      const errorResponse: WebContainerAPIResponse = {
        success: false,
        error: `Container ${containerId} not found`,
        timestamp: new Date()
      };
      return NextResponse.json(errorResponse, { status: 404 });
    }

    const body = await request.json();
    let data: any;

    switch (action) {
      case 'execute':
        if (!body.command) {
          throw new Error('Command is required');
        }
        data = await processManager.executeCommand(
          containerId,
          body.command,
          body.args || [],
          body.options || {}
        );
        break;

      case 'dev-server':
        data = await processManager.startDevServer(
          containerId,
          body.command || 'npm run dev',
          body.options || {}
        );
        break;

      case 'install':
        if (!body.packages || !Array.isArray(body.packages)) {
          throw new Error('Packages array is required');
        }
        data = await processManager.installPackages(
          containerId,
          body.packages,
          body.options || {}
        );
        break;

      case 'script':
        if (!body.scriptName) {
          throw new Error('Script name is required');
        }
        data = await processManager.runScript(
          containerId,
          body.scriptName,
          body.packageManager || 'npm'
        );
        break;

      case 'kill':
        if (!body.processId) {
          throw new Error('Process ID is required');
        }
        await processManager.killProcess(body.processId, body.signal);
        data = { message: 'Process killed successfully', processId: body.processId };
        break;

      default:
        throw new Error(`Unknown action: ${action}`);
    }

    const response: WebContainerAPIResponse = {
      success: true,
      data,
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in processes POST:', error);
    return createErrorResponse(error);
  }
}

/**
 * DELETE /api/webcontainer-api/[containerId]/processes/[processId]
 * Kill a specific process
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { containerId: string } }
): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    if (!processManager) {
      throw new Error('Process manager not initialized');
    }

    const { containerId } = params;
    const { searchParams } = new URL(request.url);
    const processId = searchParams.get('processId');
    const signal = searchParams.get('signal') || 'SIGTERM';

    if (!processId) {
      throw new Error('Process ID is required');
    }

    await processManager.killProcess(processId, signal);

    const response: WebContainerAPIResponse = {
      success: true,
      data: { message: 'Process killed successfully', processId },
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error killing process:', error);
    return createErrorResponse(error);
  }
}

// Error handling utility
function createErrorResponse(
  error: unknown,
  status: number = 500
): NextResponse {
  let errorMessage = 'Unknown error';
  let errorCode = 'UNKNOWN_ERROR';

  if (error instanceof Error) {
    errorMessage = error.message;
  }

  if (typeof error === 'object' && error !== null && 'code' in error) {
    errorCode = (error as WebContainerError).code;
  }

  const errorResponse: WebContainerAPIResponse = {
    success: false,
    error: errorMessage,
    timestamp: new Date()
  };

  return NextResponse.json(errorResponse, { status });
}
