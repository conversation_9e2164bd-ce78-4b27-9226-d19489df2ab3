/**
 * WebContainer API Routes
 * 
 * Main API endpoints for WebContainer management
 */

import { NextRequest, NextResponse } from 'next/server';
import { WebContainerManager } from '@/lib/webcontainer-runtime/core/webcontainer-manager';
import { FilesystemManager } from '@/lib/webcontainer-runtime/core/filesystem-manager';
import { ProcessManager } from '@/lib/webcontainer-runtime/core/process-manager';
import {
  WebContainerConfig,
  CreateContainerRequest,
  WebContainerAPIResponse,
  WebContainerError
} from '@/lib/webcontainer-runtime/types';

// Global manager instances (in production, these would be properly managed)
let webContainerManager: WebContainerManager | null = null;
let filesystemManager: FilesystemManager | null = null;
let processManager: ProcessManager | null = null;

// Initialize managers
async function initializeManagers() {
  if (!webContainerManager) {
    webContainerManager = new WebContainerManager();
    await webContainerManager.initialize();
    
    filesystemManager = new FilesystemManager(webContainerManager);
    processManager = new ProcessManager(webContainerManager);
  }
}

/**
 * GET /api/webcontainer-api
 * List all WebContainer instances
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    if (!webContainerManager) {
      throw new Error('WebContainer manager not initialized');
    }

    const containers = webContainerManager.listContainers();
    
    const response: WebContainerAPIResponse = {
      success: true,
      data: containers,
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error listing containers:', error);
    
    const errorResponse: WebContainerAPIResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date()
    };

    return NextResponse.json(errorResponse, { status: 500 });
  }
}

/**
 * POST /api/webcontainer-api
 * Create a new WebContainer instance
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    if (!webContainerManager) {
      throw new Error('WebContainer manager not initialized');
    }

    const body: CreateContainerRequest = await request.json();
    
    if (!body.config) {
      throw new Error('Container configuration is required');
    }

    // Validate configuration
    if (!body.config.name) {
      throw new Error('Container name is required');
    }

    const containerId = await webContainerManager.createContainer(
      body.config,
      body.files
    );

    // Mount files if provided and autoMount is enabled
    if (body.files && body.autoMount && filesystemManager) {
      await filesystemManager.mountFiles(containerId, body.files);
    }

    const response: WebContainerAPIResponse = {
      success: true,
      data: { containerId },
      timestamp: new Date()
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error('Error creating container:', error);
    
    const errorResponse: WebContainerAPIResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date()
    };

    return NextResponse.json(errorResponse, { status: 400 });
  }
}

/**
 * DELETE /api/webcontainer-api
 * Cleanup all containers (for development/testing)
 */
export async function DELETE(request: NextRequest): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    if (webContainerManager) {
      await webContainerManager.cleanup();
    }
    
    if (filesystemManager) {
      filesystemManager.cleanup();
    }
    
    if (processManager) {
      await processManager.cleanup();
    }

    // Reset managers
    webContainerManager = null;
    filesystemManager = null;
    processManager = null;

    const response: WebContainerAPIResponse = {
      success: true,
      data: { message: 'All containers cleaned up' },
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error cleaning up containers:', error);
    
    const errorResponse: WebContainerAPIResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date()
    };

    return NextResponse.json(errorResponse, { status: 500 });
  }
}

// Error handling utility
function createErrorResponse(
  error: unknown,
  status: number = 500
): NextResponse {
  let errorMessage = 'Unknown error';
  let errorCode = 'UNKNOWN_ERROR';

  if (error instanceof Error) {
    errorMessage = error.message;
  }

  if (typeof error === 'object' && error !== null && 'code' in error) {
    errorCode = (error as WebContainerError).code;
  }

  const errorResponse: WebContainerAPIResponse = {
    success: false,
    error: errorMessage,
    timestamp: new Date()
  };

  return NextResponse.json(errorResponse, { status });
}

// Validation utilities
function validateContainerConfig(config: WebContainerConfig): void {
  if (!config.name || typeof config.name !== 'string') {
    throw new Error('Container name is required and must be a string');
  }

  if (config.name.length < 1 || config.name.length > 100) {
    throw new Error('Container name must be between 1 and 100 characters');
  }

  if (config.resources) {
    if (config.resources.memory && config.resources.memory < 128) {
      throw new Error('Memory allocation must be at least 128MB');
    }

    if (config.resources.cpu && config.resources.cpu < 0.1) {
      throw new Error('CPU allocation must be at least 0.1');
    }
  }
}

// Health check endpoint
export async function HEAD(request: NextRequest): Promise<NextResponse> {
  try {
    await initializeManagers();
    
    const isHealthy = webContainerManager !== null;
    
    if (isHealthy) {
      return new NextResponse(null, { status: 200 });
    } else {
      return new NextResponse(null, { status: 503 });
    }
  } catch (error) {
    return new NextResponse(null, { status: 503 });
  }
}
