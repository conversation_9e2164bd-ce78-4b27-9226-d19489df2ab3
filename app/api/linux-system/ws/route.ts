/**
 * Linux System WebSocket API Route
 * 
 * Handles WebSocket upgrade requests for real-time system communication
 */

import { NextRequest, NextResponse } from 'next/server';
import { SystemManager } from '@/lib/linux-system-api/core/system-manager';

const systemManager = SystemManager.getInstance();

/**
 * GET /api/linux-system/ws
 * Handle WebSocket upgrade requests and provide WebSocket information
 */
export async function GET(request: NextRequest) {
  try {
    // Check if this is a WebSocket upgrade request
    const upgrade = request.headers.get('upgrade');
    const connection = request.headers.get('connection');
    
    if (upgrade?.toLowerCase() === 'websocket' && connection?.toLowerCase().includes('upgrade')) {
      // This is a WebSocket upgrade request
      // In Next.js App Router, WebSocket upgrades are typically handled differently
      // We'll redirect to the WebSocket server or provide connection information
      
      await systemManager.initialize();
      const webSocketServer = systemManager.getWebSocketServer();
      
      if (!webSocketServer) {
        return NextResponse.json(
          { error: 'WebSocket server not available' },
          { status: 503 }
        );
      }

      // Since Next.js App Router doesn't directly support WebSocket upgrades,
      // we'll provide connection information for the client to connect directly
      return NextResponse.json({
        error: 'WebSocket upgrade not supported in this route',
        message: 'Connect directly to the WebSocket server',
        websocketUrl: 'ws://localhost:3002/linux-system-ws',
        port: 3002,
        path: '/linux-system-ws'
      }, { status: 426 }); // 426 Upgrade Required
    }

    // Regular GET request - provide WebSocket information
    await systemManager.initialize();
    const webSocketServer = systemManager.getWebSocketServer();
    
    if (!webSocketServer) {
      return NextResponse.json({
        status: 'WebSocket server not running',
        available: false
      });
    }

    return NextResponse.json({
      status: 'WebSocket server running',
      available: true,
      url: 'ws://localhost:3002/linux-system-ws',
      port: 3002,
      path: '/linux-system-ws',
      connections: webSocketServer.getConnectionsCount(),
      capabilities: [
        'command_execution',
        'system_monitoring',
        'real_time_events',
        'streaming_output'
      ],
      messageTypes: [
        'ping/pong - Keep connection alive',
        'command - Execute system commands',
        'subscribe - Subscribe to events',
        'unsubscribe - Unsubscribe from events',
        'monitor - Get system information'
      ],
      events: [
        'command_output - Real-time command output',
        'system_metrics - System monitoring data',
        'package_events - Package install/remove events',
        'service_events - Service status changes',
        'process_events - Process start/stop events'
      ]
    });
  } catch (error) {
    console.error('WebSocket API error:', error);
    return NextResponse.json(
      { 
        error: 'WebSocket API error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/linux-system/ws
 * Start or configure the WebSocket server
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, ...options } = body;

    switch (action) {
      case 'start':
        await systemManager.initialize();
        const webSocketServer = systemManager.getWebSocketServer();
        
        if (webSocketServer) {
          return NextResponse.json({
            message: 'WebSocket server is already running',
            url: 'ws://localhost:3002/linux-system-ws',
            connections: webSocketServer.getConnectionsCount()
          });
        } else {
          return NextResponse.json(
            { error: 'Failed to start WebSocket server' },
            { status: 500 }
          );
        }

      case 'broadcast':
        const { eventType, data } = options;
        if (!eventType) {
          return NextResponse.json(
            { error: 'Event type is required for broadcast' },
            { status: 400 }
          );
        }

        await systemManager.initialize();
        const wsServer = systemManager.getWebSocketServer();
        
        if (wsServer) {
          wsServer.broadcast(eventType, data);
          return NextResponse.json({
            message: 'Event broadcasted successfully',
            eventType,
            connections: wsServer.getConnectionsCount()
          });
        } else {
          return NextResponse.json(
            { error: 'WebSocket server not available' },
            { status: 503 }
          );
        }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: start, broadcast' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('WebSocket POST error:', error);
    return NextResponse.json(
      { 
        error: 'WebSocket operation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
