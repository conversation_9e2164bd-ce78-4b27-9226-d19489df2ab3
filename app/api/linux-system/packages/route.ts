/**
 * Linux System Package Management API
 * 
 * Manage packages using apt (Ubuntu) or apk (Alpine)
 */

import { NextRequest, NextResponse } from 'next/server';
import { SystemManager } from '@/lib/linux-system-api/core/system-manager';

const systemManager = SystemManager.getInstance();

/**
 * GET /api/linux-system/packages
 * Get package information
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');
    const packageName = url.searchParams.get('package');
    const query = url.searchParams.get('query');

    await systemManager.initialize();
    const packageManager = systemManager.getPackageManager();

    switch (action) {
      case 'installed':
        const installedPackages = await packageManager.getInstalledPackages();
        return NextResponse.json({
          packages: installedPackages,
          count: installedPackages.length
        });

      case 'upgradable':
        const upgradablePackages = await packageManager.getUpgradablePackages();
        return NextResponse.json({
          packages: upgradablePackages,
          count: upgradablePackages.length
        });

      case 'search':
        if (!query) {
          return NextResponse.json(
            { error: 'Query parameter is required for search' },
            { status: 400 }
          );
        }
        const searchResults = await packageManager.searchPackages(query);
        return NextResponse.json({
          packages: searchResults,
          count: searchResults.length,
          query
        });

      case 'info':
        if (!packageName) {
          return NextResponse.json(
            { error: 'Package parameter is required for info' },
            { status: 400 }
          );
        }
        const packageInfo = await packageManager.getPackageInfo(packageName);
        if (packageInfo) {
          // Check if package is installed
          const isInstalled = await packageManager.isPackageInstalled(packageName);
          packageInfo.installed = isInstalled;
          return NextResponse.json(packageInfo);
        } else {
          return NextResponse.json(
            { error: 'Package not found' },
            { status: 404 }
          );
        }

      case 'check':
        if (!packageName) {
          return NextResponse.json(
            { error: 'Package parameter is required for check' },
            { status: 400 }
          );
        }
        const isInstalled = await packageManager.isPackageInstalled(packageName);
        return NextResponse.json({
          package: packageName,
          installed: isInstalled
        });

      default:
        return NextResponse.json({
          message: 'Package Management API',
          endpoints: [
            'GET /api/linux-system/packages?action=installed - Get installed packages',
            'GET /api/linux-system/packages?action=upgradable - Get upgradable packages',
            'GET /api/linux-system/packages?action=search&query=<term> - Search packages',
            'GET /api/linux-system/packages?action=info&package=<name> - Get package info',
            'GET /api/linux-system/packages?action=check&package=<name> - Check if installed',
            'POST /api/linux-system/packages - Install/remove packages',
            'PUT /api/linux-system/packages - Update/upgrade packages'
          ]
        });
    }
  } catch (error) {
    console.error('Package API error:', error);
    return NextResponse.json(
      { 
        error: 'Package operation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/linux-system/packages
 * Install or remove packages
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      action, 
      package: packageName, 
      packages = [], 
      options = {} 
    } = body;

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    await systemManager.initialize();
    const packageManager = systemManager.getPackageManager();

    switch (action) {
      case 'install':
        if (!packageName && packages.length === 0) {
          return NextResponse.json(
            { error: 'Package name or packages array is required' },
            { status: 400 }
          );
        }

        const packagesToInstall = packageName ? [packageName] : packages;
        const installResults = [];

        for (const pkg of packagesToInstall) {
          try {
            await packageManager.installPackage(pkg, options);
            installResults.push({ package: pkg, status: 'success' });
          } catch (error) {
            installResults.push({ 
              package: pkg, 
              status: 'failed', 
              error: error instanceof Error ? error.message : 'Unknown error' 
            });
          }
        }

        return NextResponse.json({
          action: 'install',
          results: installResults,
          success: installResults.every(r => r.status === 'success')
        });

      case 'remove':
        if (!packageName && packages.length === 0) {
          return NextResponse.json(
            { error: 'Package name or packages array is required' },
            { status: 400 }
          );
        }

        const packagesToRemove = packageName ? [packageName] : packages;
        const removeResults = [];

        for (const pkg of packagesToRemove) {
          try {
            await packageManager.removePackage(pkg, options);
            removeResults.push({ package: pkg, status: 'success' });
          } catch (error) {
            removeResults.push({ 
              package: pkg, 
              status: 'failed', 
              error: error instanceof Error ? error.message : 'Unknown error' 
            });
          }
        }

        return NextResponse.json({
          action: 'remove',
          results: removeResults,
          success: removeResults.every(r => r.status === 'success')
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: install, remove' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Package POST error:', error);
    return NextResponse.json(
      { 
        error: 'Package operation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/linux-system/packages
 * Update package lists or upgrade packages
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, options = {} } = body;

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    await systemManager.initialize();
    const packageManager = systemManager.getPackageManager();

    switch (action) {
      case 'update':
        await packageManager.updatePackageLists();
        return NextResponse.json({
          action: 'update',
          message: 'Package lists updated successfully'
        });

      case 'upgrade':
        await packageManager.upgradePackages(options);
        return NextResponse.json({
          action: 'upgrade',
          message: 'Packages upgraded successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: update, upgrade' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Package PUT error:', error);
    return NextResponse.json(
      { 
        error: 'Package operation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
