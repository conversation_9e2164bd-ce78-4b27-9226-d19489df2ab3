/**
 * Linux System API Routes
 * 
 * Main API endpoints for Linux system management
 */

import { NextRequest, NextResponse } from 'next/server';
import { SystemManager } from '@/lib/linux-system-api/core/system-manager';

// Initialize system manager
const systemManager = SystemManager.getInstance({
  enableMonitoring: true,
  enableWebSocket: true,
  webSocketPort: 3002,
  enableSecurity: true,
  monitoringConfig: {
    interval: 5000,
    metrics: ['cpu', 'memory', 'disk', 'network', 'processes', 'services'],
    thresholds: {
      cpu: 80,
      memory: 85,
      disk: 90
    }
  }
});

/**
 * GET /api/linux-system
 * Get system information
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');

    switch (action) {
      case 'info':
        const systemInfo = await systemManager.getSystemInfo();
        return NextResponse.json(systemInfo);

      case 'distribution':
        const distributionInfo = systemManager.getDistributionInfo();
        return NextResponse.json(distributionInfo);

      case 'status':
        return NextResponse.json({
          status: 'running',
          webSocketPort: 3002,
          monitoring: true,
          connections: systemManager.getWebSocketServer()?.getConnectionsCount() || 0
        });

      default:
        return NextResponse.json({
          message: 'Linux System API',
          version: '1.0.0',
          endpoints: [
            'GET /api/linux-system?action=info - Get system information',
            'GET /api/linux-system?action=distribution - Get distribution info',
            'GET /api/linux-system?action=status - Get API status',
            'POST /api/linux-system/command - Execute command',
            'GET /api/linux-system/packages - Package management',
            'GET /api/linux-system/services - Service management',
            'GET /api/linux-system/processes - Process management',
            'GET /api/linux-system/filesystem - File operations',
            'GET /api/linux-system/network - Network management',
            'WebSocket: ws://localhost:3002/linux-system-ws'
          ]
        });
    }
  } catch (error) {
    console.error('Linux System API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/linux-system
 * Initialize or configure the system manager
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, ...options } = body;

    switch (action) {
      case 'initialize':
        await systemManager.initialize();
        return NextResponse.json({ 
          message: 'System manager initialized successfully',
          distribution: systemManager.getDistributionInfo()
        });

      case 'shutdown':
        await systemManager.shutdown();
        return NextResponse.json({ message: 'System manager shutdown successfully' });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: initialize, shutdown' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Linux System API POST error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
