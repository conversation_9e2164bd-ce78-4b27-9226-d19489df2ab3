/**
 * Linux System Command Execution API
 * 
 * Execute commands on the Linux system with security validation
 */

import { NextRequest, NextResponse } from 'next/server';
import { SystemManager } from '@/lib/linux-system-api/core/system-manager';

const systemManager = SystemManager.getInstance();

/**
 * POST /api/linux-system/command
 * Execute a command on the system
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      command, 
      args = [], 
      options = {},
      streaming = false 
    } = body;

    if (!command) {
      return NextResponse.json(
        { error: 'Command is required' },
        { status: 400 }
      );
    }

    // Ensure system manager is initialized
    await systemManager.initialize();

    if (streaming) {
      // For streaming commands, we'll use Server-Sent Events
      const encoder = new TextEncoder();
      const stream = new ReadableStream({
        start(controller) {
          // Execute command with streaming output
          const commandExecutor = (systemManager as any).commandExecutor;
          
          commandExecutor.executeStreaming(
            {
              id: `stream_${Date.now()}`,
              command,
              args,
              ...options
            },
            (data: string, type: 'stdout' | 'stderr') => {
              const chunk = encoder.encode(`data: ${JSON.stringify({
                type: 'output',
                stream: type,
                data
              })}\n\n`);
              controller.enqueue(chunk);
            }
          ).then((result: any) => {
            // Send final result
            const chunk = encoder.encode(`data: ${JSON.stringify({
              type: 'result',
              data: result
            })}\n\n`);
            controller.enqueue(chunk);
            controller.close();
          }).catch((error: any) => {
            // Send error
            const chunk = encoder.encode(`data: ${JSON.stringify({
              type: 'error',
              error: error.message
            })}\n\n`);
            controller.enqueue(chunk);
            controller.close();
          });
        }
      });

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    } else {
      // Regular command execution
      const fullCommand = `${command} ${args.join(' ')}`.trim();
      const result = await systemManager.executeCommand(fullCommand, options);

      return NextResponse.json({
        success: true,
        result,
        command: fullCommand
      });
    }
  } catch (error) {
    console.error('Command execution error:', error);
    return NextResponse.json(
      { 
        error: 'Command execution failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/linux-system/command
 * Get running commands or command history
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');

    await systemManager.initialize();
    const commandExecutor = (systemManager as any).commandExecutor;

    switch (action) {
      case 'running':
        const runningCommands = commandExecutor.getRunningCommands();
        return NextResponse.json({
          commands: runningCommands,
          count: runningCommands.length
        });

      case 'validate':
        const command = url.searchParams.get('command');
        if (!command) {
          return NextResponse.json(
            { error: 'Command parameter is required' },
            { status: 400 }
          );
        }

        try {
          const securityValidator = (commandExecutor as any).securityValidator;
          const isDangerous = securityValidator.isDangerous(command);
          
          return NextResponse.json({
            command,
            safe: !isDangerous,
            dangerous: isDangerous
          });
        } catch (validationError) {
          return NextResponse.json({
            command,
            safe: false,
            dangerous: true,
            error: validationError instanceof Error ? validationError.message : 'Validation failed'
          });
        }

      default:
        return NextResponse.json({
          message: 'Command API',
          endpoints: [
            'POST /api/linux-system/command - Execute command',
            'GET /api/linux-system/command?action=running - Get running commands',
            'GET /api/linux-system/command?action=validate&command=<cmd> - Validate command',
            'DELETE /api/linux-system/command/<id> - Kill running command'
          ]
        });
    }
  } catch (error) {
    console.error('Command API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/linux-system/command
 * Kill a running command
 */
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const commandId = url.searchParams.get('id');

    if (!commandId) {
      return NextResponse.json(
        { error: 'Command ID is required' },
        { status: 400 }
      );
    }

    await systemManager.initialize();
    const commandExecutor = (systemManager as any).commandExecutor;
    
    const killed = await commandExecutor.killCommand(commandId);

    if (killed) {
      return NextResponse.json({
        success: true,
        message: `Command ${commandId} killed successfully`
      });
    } else {
      return NextResponse.json(
        { error: 'Command not found or already completed' },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error('Command kill error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to kill command',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
